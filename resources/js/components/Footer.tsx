import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link } from '@inertiajs/react';
import { Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter } from 'lucide-react';

interface FooterSection {
    title: string;
    links: Array<{
        title: string;
        href: string;
        external?: boolean;
    }>;
}

const footerSections: FooterSection[] = [
    {
        title: 'Services',
        links: [
            { title: 'Dedicated Teams', href: route('dedicated-teams') },
            { title: 'On-Demand Support', href: route('on-demand') },
            { title: 'Business Care Plans', href: route('business-care') },
        ],
    },
    {
        title: 'Solutions',
        links: [
            { title: 'Insurance', href: route('insurance') },
            { title: 'Finance & Accounting', href: route('finance') },
            { title: 'HR Management', href: route('hr-management') },
            { title: 'Compliance & Admin', href: route('compliance') },
        ],
    },
    {
        title: 'Company',
        links: [
            { title: 'About Us', href: route('about') },
            { title: 'Team', href: route('team') },
            { title: 'Careers', href: route('careers') },
            { title: 'Case Studies', href: route('case-studies') },
            { title: 'Blog', href: route('blog') },
        ],
    },
    {
        title: 'Support',
        links: [
            { title: 'Contact Us', href: route('contact') },
            { title: 'FAQ', href: route('faq') },
            { title: 'Documentation', href: route('documentation') },
            { title: 'Data Security', href: route('security') },
            { title: 'Privacy Policy', href: route('privacy') },
        ],
    },
];

const socialLinks = [
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Instagram, href: '#', label: 'Instagram' },
];

export function Footer() {
    return (
        <footer className="to-primary/90 relative overflow-hidden bg-gradient-to-br from-gray-900 via-gray-900 text-white">
            {/* Decorative Wave Pattern */}
            <div className="absolute top-0 right-0 left-0 h-8 overflow-hidden">
                <svg className="absolute bottom-0 h-16 w-full text-gray-900" viewBox="0 0 1200 120" preserveAspectRatio="none">
                    <path
                        d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
                        className="fill-background"
                    />
                </svg>
            </div>

            <div className="relative mx-auto max-w-7xl px-4 py-5 sm:px-6 lg:px-8">
                {/* Main Footer Content */}
                <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-6">
                    {/* Company Info */}
                    <div className="lg:col-span-2">
                        <div className="mb-6 text-2xl font-bold">
                            <img src="/images/logo.png" alt="BGS Logo" className="h-16 w-auto transition-all duration-300 hover:scale-105" />
                        </div>
                        <p className="mb-2 max-w-md leading-relaxed text-gray-300">
                            Delivering exceptional support solutions for businesses across industries. Scale smarter, grow faster with our strategic
                            partnership.
                        </p>

                        {/* Social Links */}
                        <div className="flex space-x-4 mb-2">
                            {socialLinks.map((social) => (
                                <a
                                    key={social.label}
                                    href={social.href}
                                    className="bg-primary/20 hover:bg-secondary/40 flex h-10 w-10 items-center justify-center rounded-full transition-all duration-300 hover:scale-110"
                                    aria-label={social.label}
                                >
                                    <social.icon className="h-5 w-5" />
                                </a>
                            ))}
                        </div>
                    </div>

                    {/* Footer Sections */}
                    {footerSections.map((section) => (
                        <div key={section.title}>
                            <h3 className="relative mb-5 inline-block text-lg font-semibold">
                                {section.title}
                                <span className="bg-secondary absolute -bottom-1 left-0 h-0.5 w-12"></span>
                            </h3>
                            <ul className="space-y-3">
                                {section.links.map((link) => (
                                    <li key={link.title}>
                                        {link.external ? (
                                            <a
                                                href={link.href}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="hover:text-secondary inline-block cursor-pointer text-sm text-gray-300 transition-all duration-300 hover:translate-x-1"
                                            >
                                                {link.title}
                                            </a>
                                        ) : (
                                            <Link
                                                href={link.href}
                                                className="hover:text-secondary inline-block cursor-pointer text-sm text-gray-300 transition-all duration-300 hover:translate-x-1"
                                            >
                                                {link.title}
                                            </Link>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                {/* Contact Info */}
                <div className="mt-8 flex gap -2">
                    {/* Address Info */}
                    <div className="flex flex-wrap gap-6">
                        <div className="group flex items-start space-x-3 text-gray-300">
                            <div className="bg-primary/20 group-hover:bg-primary/40 mt-1 rounded-full p-2 transition-all duration-300">
                                <MapPin className="h-4 w-4 flex-shrink-0" />
                            </div>
                            <div className="text-sm max-w-[200px]">
                                <p className="mb-2 font-medium text-white">Dubai, UAE</p>
                                <p>Paradise Building, Barsha Heights</p>
                                <p>Dubai, United Arab Emirates</p>
                            </div>
                        </div>
                        <div className="group flex items-start space-x-3 text-gray-300">
                            <div className="bg-primary/20 group-hover:bg-primary/40 mt-1 rounded-full p-2 transition-all duration-300">
                                <MapPin className="h-4 w-4 flex-shrink-0" />
                            </div>
                            <div className="text-sm max-w-[200px]">
                                <p className="mb-2 font-medium text-white">India</p>
                                <p>Corporate Court, #108 Infantry Road</p>
                                <p>Bangalore - 560 001</p>
                            </div>
                        </div>
                    </div>

                    {/* Newsletter Signup */}
                <div className="border-t border-gray-800/50 pt-4">
                    <div className="from-primary/20 to-secondary/20 rounded-xl bg-gradient-to-r p-6 backdrop-blur-sm">
                        <div className="flex flex-col items-center justify-between gap-6 md:flex-row">
                            <div className="text-center md:text-left">
                                <h3 className="mb-2 text-xl font-semibold">Stay Updated</h3>
                                <p className="text-sm text-gray-300">Get the latest insights and updates from BSG Support</p>
                            </div>
                            <div className="flex w-full max-w-md flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                                <Input
                                    type="email"
                                    placeholder="Enter your email"
                                    className="focus:ring-secondary border-gray-700 bg-white/10 text-white placeholder-gray-400"
                                />
                                <Button className="bg-secondary hover:bg-secondary/90 text-secondary-foreground">Subscribe</Button>
                            </div>
                        </div>
                    </div>
                </div>
                </div>



                {/* Bottom Bar */}
                <div className="flex flex-col items-center justify-between border-t border-gray-800/50 pt-8 md:flex-row">
                    <p className="mb-4 text-sm text-gray-400 md:mb-0">
                        &copy; {new Date().getFullYear()} Backsure Global Support. All rights reserved.
                    </p>
                    <div className="flex space-x-6 text-sm">
                        <Link href="/privacy" className="text-gray-400 transition-colors hover:text-white hover:underline">
                            Privacy Policy
                        </Link>
                        <Link href={route('terms')} className="text-gray-400 transition-colors hover:text-white hover:underline">
                            Terms of Service
                        </Link>
                        <Link href={route('cookies')} className="text-gray-400 transition-colors hover:text-white hover:underline">
                            Cookie Policy
                        </Link>
                    </div>
                </div>
            </div>
        </footer>
    );
}
