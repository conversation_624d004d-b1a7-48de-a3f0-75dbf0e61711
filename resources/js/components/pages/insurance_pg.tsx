import { Shield, FileText, RefreshCw, ClipboardCheck, Database, BarChart2, Phone, CheckCircle, Users, DollarSign, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export function InsurancePage() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <Shield className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Specialized Insurance Support
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">Insurance Operations Support</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Tailored Solutions for Brokers, Aggregators, and TPAs
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="flex space-x-4 p-2 bg-white/10 backdrop-blur-sm rounded-lg">
                                    {['Health', 'Motor', 'Life'].map((segment, index) => (
                                        <div key={index} className="px-4 py-2 rounded-md bg-white/10 text-white font-medium">
                                            {segment}
                                        </div>
                                    ))}
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    <div className="max-w-4xl mx-auto">
                        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                            At BackSure Global Support, we specialize in providing skilled backend teams for the insurance industry - helping your business deliver faster, smarter, and more reliable results.
                            Our services are designed to reduce administrative load, improve policy accuracy, and streamline customer servicing - supporting brokers, aggregators, and TPAs across Health, Motor, and Life segments.
                            Whether you're a growing brokerage or an established aggregator, we help you scale operations without increasing overhead.
                        </p>
                    </div>

                    {/* Specialized Services Section with Hover Effects */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Shield className="h-7 w-7 text-[#b19763]" />
                            Our Specialized Services Include
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {[
                                {
                                    icon: <Phone className="h-5 w-5 text-[#b19763]" />,
                                    title: "Sales Support",
                                    description: "Lead follow-ups, policy explanations, and coordination between clients and insurers — improving conversion rates."
                                },
                                {
                                    icon: <FileText className="h-5 w-5 text-[#b19763]" />,
                                    title: "Underwriting Assistance",
                                    description: "Collecting and verifying customer documents, pre-screening applications, and coordinating approvals."
                                },
                                {
                                    icon: <RefreshCw className="h-5 w-5 text-[#b19763]" />,
                                    title: "Policy Processing & Renewals",
                                    description: "Complete process handling from new policy issuance to managing renewal cycles with timely follow-ups."
                                },
                                {
                                    icon: <ClipboardCheck className="h-5 w-5 text-[#b19763]" />,
                                    title: "Claims Coordination",
                                    description: "Liaison between clients, insurers, and TPAs — tracking claim statuses and ensuring smooth resolution."
                                },
                                {
                                    icon: <Database className="h-5 w-5 text-[#b19763]" />,
                                    title: "CRM Management",
                                    description: "Maintain clean and updated CRM records across platforms like Zoho CRM, Salesforce, and industry systems."
                                },
                                {
                                    icon: <FileText className="h-5 w-5 text-[#b19763]" />,
                                    title: "Quote Preparation",
                                    description: "Preparing comparative insurance quotes by collecting information from multiple insurance partners."
                                },
                                {
                                    icon: <BarChart2 className="h-5 w-5 text-[#b19763]" />,
                                    title: "Data Entry & Reporting",
                                    description: "Daily data updates, MIS reporting, and summary dashboards for real-time business insights.",
                                    fullWidth: true
                                }
                            ].map((service, index) => (
                                <motion.div 
                                    key={index} 
                                    variants={fadeIn}
                                    className={`group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300 ${service.fullWidth ? 'md:col-span-2' : ''}`}
                                >
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            {service.icon}
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767] group-hover:text-[#062767]">{service.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">{service.description}</p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Why It Matters Section with Animated Icons */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <CheckCircle className="h-7 w-7 text-[#b19763]" />
                            Why It Matters
                        </motion.h3>

                        <div className="grid md:grid-cols-3 gap-8">
                            {[
                                {
                                    icon: <Shield className="h-10 w-10 text-[#b19763]" />,
                                    title: "Industry-Trained Teams",
                                    description: "Our staff is trained specifically for the insurance industry — reducing errors and increasing turnaround speed."
                                },
                                {
                                    icon: <DollarSign className="h-10 w-10 text-[#b19763]" />,
                                    title: "Operational Cost Savings",
                                    description: "Scale your backend without hiring in-house. We offer cost-effective solutions with performance accountability."
                                },
                                {
                                    icon: <Users className="h-10 w-10 text-[#b19763]" />,
                                    title: "Better Client Experience",
                                    description: "Faster responses, fewer errors, and clean data ensure a smoother client journey from quote to claim."
                                }
                            ].map((benefit, index) => (
                                <motion.div 
                                    key={index} 
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5 + (index * 0.1), duration: 0.5 }}
                                    className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                                >
                                    <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                        {benefit.icon}
                                    </div>
                                    <h4 className="text-xl font-semibold text-[#062767] mb-3">{benefit.title}</h4>
                                    <p className="text-gray-600">{benefit.description}</p>
                                </motion.div>
                            ))}
                        </div>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1450101499163-c8848c66ca85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Insurance Support That Works Like Your In-House Team
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                Choose BSG Support to power your insurance operations with reliable, trained, and performance-driven backend teams.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <Phone className="h-5 w-5" />
                                Request a Consultation
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}