import { Clock, DollarSign, FileText, ClipboardList, TrendingUp, Zap, Calendar, Users, CheckCircle, Check, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export function OnDemandPage() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <Zap className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Flexible Support Solutions
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">On-Demand Support</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Flexible Support When You Need It Most
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                                    <Check className="h-5 w-5 text-[#b19763] mr-2" />
                                    <span className="text-white font-medium">Flexible • Scalable • Cost-Effective</span>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    <div className="max-w-4xl mx-auto">
                        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                            Not every business need requires a full-time team. Our On-Demand Support model gives you access to skilled professionals 
                            exactly when you need them-whether it's for a few hours a day or a specific project. Pay only for the time you use, 
                            while maintaining the same quality and reliability you'd expect from an in-house team.
                        </p>
                    </div>

                    {/* Key Services Section with Hover Effects */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Layers className="h-7 w-7 text-[#b19763]" />
                            Key Services:
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {/* Service 1 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <Headphones className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Customer Support</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Handle customer inquiries, process claims, and provide exceptional service during peak periods or after hours.
                                </p>
                            </motion.div>

                            {/* Service 2 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <ClipboardList className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Data Processing & Entry</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Efficiently process applications, claims, and other data-intensive tasks with accuracy and attention to detail.
                                </p>
                            </motion.div>

                            {/* Service 3 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <Calendar className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Seasonal Support</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Scale up your team during busy seasons or renewal periods without the long-term commitment of hiring full-time staff.
                                </p>
                            </motion.div>

                            {/* Service 4 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <Briefcase className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Project-Based Work</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Get specialized support for specific projects or initiatives without adding permanent headcount to your organization.
                                </p>
                            </motion.div>
                        </motion.div>
                    </div>

                    {/* Why It Works Section with Animated Icons */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Check className="h-7 w-7 text-[#b19763]" />
                            Why It Works:
                        </motion.h3>

                        <div className="grid md:grid-cols-4 gap-8">
                            {/* Reason 1 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <DollarSign className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Cost Efficiency</h4>
                                <p className="text-gray-600">
                                    Pay only for the hours you need, eliminating overhead costs associated with full-time employees.
                                </p>
                            </motion.div>

                            {/* Reason 2 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.6, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <Zap className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Rapid Deployment</h4>
                                <p className="text-gray-600">
                                    Quickly access skilled professionals without lengthy recruitment and onboarding processes.
                                </p>
                            </motion.div>

                            {/* Reason 3 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <BarChart2 className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Flexible Scaling</h4>
                                <p className="text-gray-600">
                                    Easily adjust your support levels up or down based on changing business needs and demands.
                                </p>
                            </motion.div>

                            {/* Reason 4 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.8, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <Clock className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Extended Hours</h4>
                                <p className="text-gray-600">
                                    Provide support outside your normal business hours without requiring shift work from your core team.
                                </p>
                            </motion.div>
                        </div>
                    </div>

                    {/* Perfect For Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                            className="text-2xl font-bold text-[#062767] mb-6 border-b pb-4 border-gray-100">Who It's Perfect For:</motion.h3>
                        <p className="text-gray-700 mb-4">
                            Our On-Demand Support is ideal for:
                        </p>
                        <motion.ul 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-4 text-gray-700">
                            <motion.li variants={fadeIn} className="flex items-start gap-3">
                                <div className="p-1 bg-[#f8f5ee] rounded-full mt-1">
                                    <Check className="h-4 w-4 text-[#b19763]" />
                                </div>
                                <span>Businesses with fluctuating workloads or seasonal peaks</span>
                            </motion.li>
                            <motion.li variants={fadeIn} className="flex items-start gap-3">
                                <div className="p-1 bg-[#f8f5ee] rounded-full mt-1">
                                    <Check className="h-4 w-4 text-[#b19763]" />
                                </div>
                                <span>Companies looking to extend their service hours without adding shifts</span>
                            </motion.li>
                            <motion.li variants={fadeIn} className="flex items-start gap-3">
                                <div className="p-1 bg-[#f8f5ee] rounded-full mt-1">
                                    <Check className="h-4 w-4 text-[#b19763]" />
                                </div>
                                <span>Organizations with specific project needs or temporary staffing requirements</span>
                            </motion.li>
                            <motion.li variants={fadeIn} className="flex items-start gap-3">
                                <div className="p-1 bg-[#f8f5ee] rounded-full mt-1">
                                    <Check className="h-4 w-4 text-[#b19763]" />
                                </div>
                                <span>Startups and small businesses that need professional support without full-time commitments</span>
                            </motion.li>
                        </motion.ul>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Get Support When You Need It Most
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                Flexible, scalable support that adapts to your business needs without long-term commitments.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <Zap className="h-5 w-5" />
                                Get Started
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}