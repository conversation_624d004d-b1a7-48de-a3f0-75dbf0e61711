import { Mail } from 'lucide-react';

export default function CookiesPage() {
    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Page Header */}
				<div className="bg-[#b19763] py-16 px-4 mb-12 rounded-lg">
				    <div className="max-w-3xl mx-auto text-center">
				        <h2 className="text-4xl font-bold text-white mb-4">Cookie Policy</h2>
				        <p className="text-xl text-white opacity-90">
				            Information about how we use cookies on our website.
				        </p>
				    </div>
				</div>

				{/* Cookies Content */}
				<div className="bg-white rounded-lg shadow-md p-8 mb-12">
				    <p className="text-lg text-gray-700 mb-6">
				        This Cookie Policy explains how Backsure Global Support uses cookies and similar technologies to recognize you when you visit our website.
				    </p>
				    <p className="text-lg text-gray-700 mb-6">
				        It explains what these technologies are and why we use them, as well as your rights to control our use of them.
				    </p>
				    <h3 className="text-xl font-semibold text-[#062767] mt-8 mb-4">What Are Cookies?</h3>
				    <p className="text-lg text-gray-700 mb-6">
				        Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners in order to make their websites work, or to work more efficiently, as well as to provide reporting information.
				    </p>
				    <h3 className="text-xl font-semibold text-[#062767] mt-8 mb-4">How We Use Cookies</h3>
				    <p className="text-lg text-gray-700 mb-6">
				        We use cookies for several reasons. Some cookies are required for technical reasons in order for our website to operate, and we refer to these as "essential" or "strictly necessary" cookies. Other cookies also enable us to track and target the interests of our users to enhance the experience on our website.
				    </p>
				</div>

                {/* Closing CTA */}
                <div className="bg-[#f8f5ee] p-8 rounded-lg text-center">
                    <h3 className="text-2xl font-bold text-[#062767] mb-4">
                        Questions About Our Cookie Policy?
                    </h3>
                    <p className="text-black mb-6 max-w-3xl mx-auto">
                        Contact our team for more information about how we use cookies.
                    </p>
                    <button className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2"
                    	onClick={() => window.location.href = "/contact"}
                    >
                        <Mail className="h-5 w-5" />
                        Contact Us
                    </button>
                </div>
            </div>
        </section>
    );
}
