import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChevronDown,
  CheckCircle,
  HelpCircle,
  FileText,
  Clock,
  DollarSign,
  Shield,
  Users,
  Briefcase,
  Headphones,
  Settings
} from 'lucide-react';

interface FaqItem {
  question: string;
  answer: string;
  category: string;
  icon: React.ComponentType<{ className?: string }>;
}

const faqItems: FaqItem[] = [
  {
    question: "What services does Backsure Global Support provide?",
    answer: "Backsure Global Support offers a comprehensive range of business support services including administrative assistance, data entry, customer service, compliance support, and more. Our dedicated team works as an extension of your business to handle back-office operations efficiently.",
    category: "Services",
    icon: Briefcase
  },
  {
    question: "How does the dedicated employee model work?",
    answer: "Our dedicated employee model provides you with staff members who work exclusively for your business. These professionals are hired, trained, and managed by us but dedicated to your operations. This gives you the benefits of having full-time staff without the administrative burden of direct employment.",
    category: "Services",
    icon: Users
  },
  {
    question: "What industries do you serve?",
    answer: "We serve a wide range of industries including insurance, financial services, healthcare, real estate, e-commerce, and professional services. Our solutions are customized to meet the specific needs and compliance requirements of each industry.",
    category: "Services",
    icon: Briefcase
  },
  {
    question: "How quickly can you onboard new dedicated employees?",
    answer: "Our typical onboarding process takes 2-4 weeks from initial consultation to having your dedicated employees fully operational. This includes recruitment, training on your specific processes, and integration with your systems.",
    category: "Process",
    icon: Clock
  },
  {
    question: "What is the minimum contract period?",
    answer: "Our standard agreements start with a 6-month minimum term, which allows sufficient time for team members to become fully integrated with your operations and deliver optimal value. After this initial period, we offer flexible month-to-month arrangements.",
    category: "Contracts",
    icon: FileText
  },
  {
    question: "How do you ensure quality and performance?",
    answer: "We implement comprehensive quality assurance processes including regular performance reviews, ongoing training, and detailed reporting. We also establish clear KPIs aligned with your business objectives and provide transparent performance metrics.",
    category: "Quality",
    icon: CheckCircle
  },
  {
    question: "What about data security and confidentiality?",
    answer: "We maintain strict data security protocols compliant with international standards. All our employees sign comprehensive confidentiality agreements, and we implement robust technical safeguards including secure access controls, encryption, and regular security audits.",
    category: "Security",
    icon: Shield
  },
  {
    question: "How is pricing structured?",
    answer: "Our pricing is transparent and based on the number of dedicated employees, their skill levels, and the complexity of tasks. We offer monthly subscription models with no hidden fees. Custom packages are available for businesses with specific requirements.",
    category: "Pricing",
    icon: DollarSign
  },
  {
    question: "Can I scale my team up or down as needed?",
    answer: "Yes, flexibility is a key benefit of our service. You can scale your dedicated team up or down with 30 days' notice, allowing you to adapt to changing business needs, seasonal demands, or growth opportunities without the complexities of traditional hiring or downsizing.",
    category: "Flexibility",
    icon: Settings
  },
  {
    question: "How do I communicate with my dedicated team?",
    answer: "We provide multiple communication channels including email, phone, video conferencing, and project management tools. Your team will adapt to your preferred communication methods and work during hours that align with your business operations.",
    category: "Communication",
    icon: Headphones
  }
];

export function FaqPage() {
  const [openItem, setOpenItem] = useState<number | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [filter, setFilter] = useState<string>('All');

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const toggleItem = (index: number) => {
    setOpenItem(openItem === index ? null : index);
  };

  const categories = ['All', ...Array.from(new Set(faqItems.map(item => item.category)))];
  const filteredFaqs = filter === 'All' ? faqItems : faqItems.filter(item => item.category === filter);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section with Parallax Effect */}
        <div className="relative overflow-hidden rounded-2xl mb-16">
          <div className="absolute inset-0 bg-cover bg-center" 
               style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1674&q=80)', 
                      filter: 'brightness(0.4)' }}>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
          <motion.div 
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            variants={fadeIn}
            transition={{ duration: 0.6 }}
            className="relative py-20 px-6 text-center">
            <div className="max-w-3xl mx-auto">
              <motion.div 
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                <span className="text-white/90 text-sm font-medium flex items-center">
                  <HelpCircle className="h-4 w-4 mr-2 text-[#b19763]" />
                  Frequently Asked Questions
                </span>
              </motion.div>
              <h2 className="text-5xl font-bold text-white mb-6">How Can We Help You?</h2>
              <p className="text-xl text-white/90 mb-8">
                Find answers to common questions about our services and solutions
              </p>
              <motion.div 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="flex justify-center">
                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                  <CheckCircle className="h-5 w-5 text-[#b19763] mr-2" />
                  <span className="text-white font-medium">Clear • Comprehensive • Helpful</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Main Content with FAQ Accordion */}
        <motion.div 
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          variants={fadeIn}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
          
          {/* Category Filter */}
          <div className="mb-10">
            <h3 className="text-xl font-semibold text-[#062767] mb-6">Browse by Category</h3>
            <div className="flex flex-wrap gap-3">
              {categories.map((category, index) => (
                <button
                  key={index}
                  onClick={() => setFilter(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${filter === category ? 'bg-[#062767] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* FAQ Accordion */}
          <motion.div 
            variants={staggerContainer}
            initial="hidden"
            animate="visible"
            className="space-y-4">
            {filteredFaqs.map((faq, index) => (
              <motion.div 
                key={index}
                variants={fadeIn}
                className={`border rounded-xl overflow-hidden transition-all duration-300 ${openItem === index ? 'border-[#b19763]/30 shadow-md' : 'border-gray-200'}`}
              >
                <button
                  onClick={() => toggleItem(index)}
                  className={`w-full flex items-center justify-between p-5 text-left ${openItem === index ? 'bg-[#f8f5ee]' : 'bg-white hover:bg-gray-50'}`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${openItem === index ? 'bg-[#b19763]/10' : 'bg-gray-100'} transition-colors duration-300`}>
                      <faq.icon className={`h-5 w-5 ${openItem === index ? 'text-[#b19763]' : 'text-gray-500'}`} />
                    </div>
                    <span className={`font-medium ${openItem === index ? 'text-[#062767]' : 'text-gray-700'}`}>{faq.question}</span>
                  </div>
                  <ChevronDown className={`h-5 w-5 text-gray-500 transition-transform duration-300 ${openItem === index ? 'rotate-180 text-[#b19763]' : ''}`} />
                </button>
                <div 
                  className={`overflow-hidden transition-all duration-300 ${openItem === index ? 'max-h-96' : 'max-h-0'}`}
                >
                  <div className="p-5 pt-0 bg-[#f8f5ee]/50">
                    <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Still Have Questions Section */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="mt-16 relative overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
            <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1560264280-88b68371db39?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
            <div className="relative p-10 text-center">
              <h3 className="text-3xl font-bold text-white mb-4">
                Still Have Questions?
              </h3>
              <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                Our team is ready to provide personalized answers to your specific questions about our services and how we can support your business.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button 
                  className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                  onClick={() => window.location.href = "mailto:<EMAIL>"}
                >
                  <Headphones className="h-5 w-5" />
                  Contact Support
                </button>
                <button 
                  className="bg-[#b19763] hover:bg-[#c9ad7a] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                  onClick={() => window.location.href = "/contact"}
                >
                  <Users className="h-5 w-5" />
                  Schedule a Consultation
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}