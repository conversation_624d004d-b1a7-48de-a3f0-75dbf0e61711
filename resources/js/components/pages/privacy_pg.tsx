import { BookOpen, Mail, Calendar, ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';

export function PrivacyPage() {


    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Page Header */}
				<div className="bg-[#b19763] py-16 px-4 mb-12 rounded-lg">
				    <div className="max-w-3xl mx-auto text-center">
				        <h2 className="text-4xl font-bold text-white mb-4">Privacy Policy</h2>
				        <p className="text-xl text-white opacity-90">
				            Please Contact Us.
				        </p>
				    </div>
				</div>     
				
				
                {/* Closing CTA */}
                <div className="bg-[#f8f5ee] p-8 rounded-lg text-center">
                    <h3 className="text-2xl font-bold text-[#062767] mb-4">
                        Numbers Made Simple. Finances Done Right.
                    </h3>
                    <p className="text-black mb-6 max-w-3xl mx-auto">
                        Let us manage your finance and accounting while you focus on business growth with confidence.
                    </p>
                    <button className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2"
                    	onClick={() => window.location.href = "/pg_layouts/contact"}
                    
                    >
                        <Mail className="h-5 w-5" />
                        Request a Consultation
                    </button>
                </div>				
				           
            </div>
        </section>
    );
}