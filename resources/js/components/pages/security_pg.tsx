import { Shield, Lock, FileText, Users, Monitor, Server, FileLock, ClipboardCheck, ShieldCheck, ShieldAlert, Database, Eye, EyeOff, Fingerprint, HardDrive, Key } from 'lucide-react';
import { motion } from 'framer-motion';
import { useState } from 'react';

export function SecurityPage() {
    const [activeSection, setActiveSection] = useState<string | null>(null);
    
    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { 
            opacity: 1, 
            y: 0,
            transition: { duration: 0.6 }
        }
    };
    
    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.2
            }
        }
    };
    
    const iconAnimation = {
        hidden: { scale: 0.8, opacity: 0 },
        visible: { 
            scale: 1, 
            opacity: 1,
            transition: { 
                type: "spring",
                stiffness: 100,
                damping: 10
            }
        },
        hover: { 
            scale: 1.1,
            rotate: [0, -5, 5, -5, 0],
            transition: { duration: 0.3 }
        }
    };
    
    const handleSectionHover = (section: string) => {
        setActiveSection(section);
    };
    
    const handleSectionLeave = () => {
        setActiveSection(null);
    };
    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Page Header */}

                <motion.div 
                    initial="hidden"
                    animate="visible"
                    variants={fadeIn}
                    className="bg-gradient-to-r from-[#b19763] to-[#d4b77d] py-16 px-4 mb-12 rounded-lg relative overflow-hidden shadow-lg"
                >
                    {/* Animated background elements */}
                    <motion.div 
                        className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -mt-20 -mr-20"
                        animate={{ 
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.2, 0.3]
                        }}
                        transition={{ 
                            duration: 8, 
                            repeat: Infinity,
                            repeatType: "reverse" 
                        }}
                    />
                    <motion.div 
                        className="absolute bottom-0 left-0 w-40 h-40 bg-white/10 rounded-full -mb-10 -ml-10"
                        animate={{ 
                            scale: [1, 1.3, 1],
                            opacity: [0.2, 0.3, 0.2]
                        }}
                        transition={{ 
                            duration: 6, 
                            repeat: Infinity,
                            repeatType: "reverse",
                            delay: 1
                        }}
                    />
                    
                    <div className="max-w-3xl mx-auto text-center relative z-10">
                        <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                            className="flex justify-center mb-6"
                        >
                            <div className="bg-white/20 p-4 rounded-full">
                                <ShieldCheck className="h-12 w-12 text-white" />
                            </div>
                        </motion.div>
                        <motion.h2 
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            className="text-4xl font-bold text-white mb-4"
                        >
                            Data Security at BGS
                        </motion.h2>
                        <motion.p 
                            initial={{ y: 20, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.4, duration: 0.5 }}
                            className="text-xl text-white"
                        >
                            Protecting Your Information with Confidence
                        </motion.p>
                    </div>
                </motion.div>
                
                {/* Main Content */}
                <motion.div 
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true, margin: "-100px" }}
                    variants={fadeIn}
                    className="bg-white rounded-lg shadow-lg p-8 mb-12 border border-gray-100"
                >
                    <motion.div 
                        className="max-w-3xl mx-auto space-y-8"
                        variants={staggerContainer}
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true }}
                    >
                        <motion.div className="flex items-start gap-4" variants={fadeIn}>
                            <motion.div 
                                className="bg-[#f8f5ee] p-3 rounded-full mt-1 flex-shrink-0"
                                variants={iconAnimation}
                                whileHover="hover"
                            >
                                <Database className="h-6 w-6 text-[#b19763]" />
                            </motion.div>
                            <p className="text-lg text-black">
                                At Backsure Global Support, we understand that managing your daily business operations often involves handling sensitive, confidential data. Whether we work through our secure systems or access your company's platforms (such as CRMs, ERPs, or proprietary tools), data security remains our top priority.
                            </p>
                        </motion.div>
                        
                        <motion.div className="flex items-start gap-4" variants={fadeIn}>
                            <motion.div 
                                className="bg-[#f8f5ee] p-3 rounded-full mt-1 flex-shrink-0"
                                variants={iconAnimation}
                                whileHover="hover"
                            >
                                <ShieldAlert className="h-6 w-6 text-[#b19763]" />
                            </motion.div>
                            <p className="text-lg text-black">
                                We follow a multi-layered security approach—combining legal safeguards, digital protections, and physical access control—to ensure your data is managed responsibly, securely, and in full compliance with international and MENA region regulations.
                            </p>
                        </motion.div>
                        
                        <motion.div className="flex items-start gap-4" variants={fadeIn}>
                            <motion.div 
                                className="bg-[#f8f5ee] p-3 rounded-full mt-1 flex-shrink-0"
                                variants={iconAnimation}
                                whileHover="hover"
                            >
                                <FileText className="h-6 w-6 text-[#b19763]" />
                            </motion.div>
                            <p className="text-lg text-black">
                                Every client engagement begins with a Non-Disclosure Agreement (NDA), and each team member is bound by individual confidentiality agreements before working on any process.
                            </p>
                        </motion.div>
                    </motion.div>
                </motion.div>

                    {/* Our Data Security Framework */}
                    <motion.div 
                        className="mb-12"
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true, margin: "-100px" }}
                        variants={fadeIn}
                    >
                        <motion.div 
                            className="flex items-center gap-3 mb-8 bg-[#062767]/5 p-4 rounded-lg inline-block"
                            initial={{ x: -20, opacity: 0 }}
                            whileInView={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                whileHover={{ rotate: 360 }}
                                transition={{ duration: 0.6 }}
                            >
                                <Shield className="h-8 w-8 text-[#b19763]" />
                            </motion.div>
                            <h3 className="text-2xl font-bold text-[#062767]">
                                Our Data Security Framework
                            </h3>
                        </motion.div>

                        <motion.div 
                            className="grid md:grid-cols-3 gap-6"
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                        >
                            {/* Legal Safeguards */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('legal')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <FileText className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Legal Safeguards</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>NDA signed with every client</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Confidentiality agreements signed by all employees</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Internal protocols to manage sensitive and regulated information</span>
                                    </motion.li>
                                </ul>
                            </motion.div>

                            {/* Digital Security */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('digital')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <Lock className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Digital Security (MENA Compliant)</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>End-to-end encryption for all communications and file exchanges</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>VPN tunnels for secure connectivity</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Firewall protection to block unauthorized access</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Role-based user access and real-time audit logs</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.5 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Data handling aligned with UAE PDPL and GCC privacy laws</span>
                                    </motion.li>
                                </ul>
                            </motion.div>

                            {/* Physical Security */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('physical')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <Fingerprint className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Physical Security Measures</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>SmartCard and login-based entry to secure workspaces</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>CCTV-monitored facilities with 24/7 security</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Controlled device usage within operational areas</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Regular internal audits and infrastructure reviews</span>
                                    </motion.li>
                                </ul>
                            </motion.div>
                        </motion.div>
                    </motion.div>

                    {/* Platform Access Protocols */}
                    <motion.div 
                        className="mb-12"
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true, margin: "-100px" }}
                        variants={fadeIn}
                    >
                        <motion.div 
                            className="flex items-center gap-3 mb-8 bg-[#062767]/5 p-4 rounded-lg inline-block"
                            initial={{ x: -20, opacity: 0 }}
                            whileInView={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                whileHover={{ rotate: 360 }}
                                transition={{ duration: 0.6 }}
                            >
                                <Key className="h-8 w-8 text-[#b19763]" />
                            </motion.div>
                            <h3 className="text-2xl font-bold text-[#062767]">
                                Platform Access Protocols
                            </h3>
                        </motion.div>

                        <motion.p 
                            className="text-black mb-6"
                            variants={fadeIn}
                        >
                            If you provide access to your own platforms (e.g., CRM, ERP, shared drives), we follow strict internal access protocols, including:
                        </motion.p>

                        <motion.div 
                            className="grid md:grid-cols-2 gap-6"
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                        >
                            <motion.ul className="space-y-3 text-black" variants={fadeIn}>
                                <motion.li 
                                    className="flex items-start gap-2"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                >
                                    <motion.div 
                                        className="bg-[#b19763]/10 p-1 rounded-full mt-0.5"
                                        whileHover={{ scale: 1.2 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                    </motion.div>
                                    <span>Dedicated user credentials with access limited by role</span>
                                </motion.li>
                                <motion.li 
                                    className="flex items-start gap-2"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <motion.div 
                                        className="bg-[#b19763]/10 p-1 rounded-full mt-0.5"
                                        whileHover={{ scale: 1.2 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                    </motion.div>
                                    <span>Activity logging to ensure traceability</span>
                                </motion.li>
                            </motion.ul>
                            <motion.ul className="space-y-3 text-black" variants={fadeIn}>
                                <motion.li 
                                    className="flex items-start gap-2"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.1 }}
                                >
                                    <motion.div 
                                        className="bg-[#b19763]/10 p-1 rounded-full mt-0.5"
                                        whileHover={{ scale: 1.2 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                    </motion.div>
                                    <span>No unauthorized downloads or local storage unless approved</span>
                                </motion.li>
                                <motion.li 
                                    className="flex items-start gap-2"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: 0.2 }}
                                >
                                    <motion.div 
                                        className="bg-[#b19763]/10 p-1 rounded-full mt-0.5"
                                        whileHover={{ scale: 1.2 }}
                                        transition={{ duration: 0.3 }}
                                    >
                                        <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                    </motion.div>
                                    <span>Immediate access revocation upon role change or offboarding</span>
                                </motion.li>
                            </motion.ul>
                        </motion.div>

                        <motion.p 
                            className="text-black mt-6"
                            variants={fadeIn}
                        >
                            Our team members are regularly trained on data handling best practices and understand the importance of maintaining client confidentiality at all times.
                        </motion.p>
                    </motion.div>

                    {/* Ongoing Data Handling */}
                    <motion.div 
                        className="mb-12"
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true, margin: "-100px" }}
                        variants={fadeIn}
                    >
                        <motion.div 
                            className="flex items-center gap-3 mb-8 bg-[#062767]/5 p-4 rounded-lg inline-block"
                            initial={{ x: -20, opacity: 0 }}
                            whileInView={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                whileHover={{ rotate: 360 }}
                                transition={{ duration: 0.6 }}
                            >
                                <Database className="h-8 w-8 text-[#b19763]" />
                            </motion.div>
                            <h3 className="text-2xl font-bold text-[#062767]">
                                Ongoing Data Handling & Optional Destruction
                            </h3>
                        </motion.div>

                        <motion.p 
                            className="text-black mb-6"
                            variants={fadeIn}
                        >
                            As part of daily operations, we continuously monitor and secure all client data accessed or processed by our teams. Where requested, we follow a secure data destruction protocol-removing stored information and providing proof of deletion to meet your internal or regulatory requirements.
                        </motion.p>

                        <motion.div 
                            className="grid md:grid-cols-4 gap-6"
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                        >
                            {/* Secure Data Storage */}
                            <motion.div className="bg-gray-50 p-6 rounded-lg" variants={fadeIn} whileHover={{ y: -5 }} transition={{ duration: 0.3 }}>
                                <h4 className="text-lg font-semibold text-[#062767] mb-3">Secure Data Storage</h4>
                                <p className="text-black">
                                    All client data is stored in encrypted formats with multiple security layers to prevent unauthorized access or breaches.
                                </p>
                            </motion.div>

                            {/* Access Control */}
                            <motion.div className="bg-gray-50 p-6 rounded-lg" variants={fadeIn} whileHover={{ y: -5 }} transition={{ duration: 0.3 }}>
                                <h4 className="text-lg font-semibold text-[#062767] mb-3">Access Control</h4>
                                <p className="text-black">
                                    Strict role-based access ensures only authorized team members can view or process specific data needed for their tasks.
                                </p>
                            </motion.div>

                            {/* Regular Audits */}
                            <motion.div className="bg-gray-50 p-6 rounded-lg" variants={fadeIn} whileHover={{ y: -5 }} transition={{ duration: 0.3 }}>
                                <h4 className="text-lg font-semibold text-[#062767] mb-3">Regular Audits</h4>
                                <p className="text-black">
                                    We conduct scheduled security audits to verify compliance and identify any potential vulnerabilities before they become issues.
                                </p>
                            </motion.div>

                            {/* Data Destruction */}
                            <motion.div className="bg-gray-50 p-6 rounded-lg" variants={fadeIn} whileHover={{ y: -5 }} transition={{ duration: 0.3 }}>
                                <h4 className="text-lg font-semibold text-[#062767] mb-3">Data Destruction</h4>
                                <p className="text-black">
                                    Upon request or project completion, we can permanently delete client data with verification certificates provided for your records.
                                </p>
                            </motion.div>
                        </motion.div>
                    </motion.div>

                    {/* Our Comprehensive Security Measures */}
                    <motion.div 
                        className="mb-12"
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true, margin: "-100px" }}
                        variants={fadeIn}
                    >
                        <motion.div 
                            className="flex items-center gap-3 mb-8 bg-[#062767]/5 p-4 rounded-lg inline-block"
                            initial={{ x: -20, opacity: 0 }}
                            whileInView={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.2 }}
                            viewport={{ once: true }}
                        >
                            <motion.div
                                whileHover={{ rotate: 360 }}
                                transition={{ duration: 0.6 }}
                            >
                                <ShieldAlert className="h-8 w-8 text-[#b19763]" />
                            </motion.div>
                            <h3 className="text-2xl font-bold text-[#062767]">
                                Our Comprehensive Security Measures
                            </h3>
                        </motion.div>

                        <motion.div 
                            className="grid md:grid-cols-3 gap-6"
                            variants={staggerContainer}
                            initial="hidden"
                            whileInView="visible"
                            viewport={{ once: true }}
                        >
                            {/* Technical Framework */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('technical')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <Server className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Technical Framework</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Multi-layered network security</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Advanced threat detection systems</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Regular vulnerability assessments</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Secure cloud infrastructure</span>
                                    </motion.li>
                                </ul>
                            </motion.div>

                            {/* Personnel Framework */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('personnel')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <Users className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Personnel Framework</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Background checks for all employees</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Regular security awareness training</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Strict access control policies</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Confidentiality agreements</span>
                                    </motion.li>
                                </ul>
                            </motion.div>

                            {/* Compliance Framework */}
                            <motion.div 
                                className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
                                variants={fadeIn}
                                whileHover={{ y: -5 }}
                                transition={{ duration: 0.3 }}
                                onMouseEnter={() => handleSectionHover('compliance')}
                                onMouseLeave={handleSectionLeave}
                            >
                                <div className="flex items-center gap-3 mb-4">
                                    <motion.div 
                                        className="bg-[#f8f5ee] p-3 rounded-full"
                                        variants={iconAnimation}
                                        whileHover="hover"
                                    >
                                        <ClipboardCheck className="h-5 w-5 text-[#b19763]" />
                                    </motion.div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Compliance Framework</h4>
                                </div>
                                <ul className="space-y-3 text-black">
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Adherence to MENA data protection laws</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.2 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Alignment with international standards</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.3 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Regular compliance audits</span>
                                    </motion.li>
                                    <motion.li 
                                        className="flex items-start gap-2"
                                        initial={{ opacity: 0, x: -10 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        transition={{ delay: 0.4 }}
                                    >
                                        <div className="bg-[#b19763]/10 p-1 rounded-full mt-0.5">
                                            <div className="w-2 h-2 bg-[#b19763] rounded-full"></div>
                                        </div>
                                        <span>Documented security policies</span>
                                    </motion.li>
                                </ul>
                            </motion.div>
                        </motion.div>
                    </motion.div>

                    {/* Closing CTA */}
                    <motion.div 
                        className="bg-gradient-to-br from-[#f8f5ee] to-white p-10 rounded-lg text-center shadow-sm border border-gray-100"
                        initial="hidden"
                        whileInView="visible"
                        viewport={{ once: true }}
                        variants={fadeIn}
                        whileHover={{ boxShadow: '0 10px 40px rgba(0, 0, 0, 0.05)' }}
                        transition={{ duration: 0.5 }}
                    >
                        <motion.div
                            initial={{ scale: 0.9, opacity: 0 }}
                            whileInView={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                            viewport={{ once: true }}
                            className="mb-6"
                        >
                            <motion.div 
                                className="w-16 h-16 bg-[#062767]/5 rounded-full flex items-center justify-center mx-auto mb-4"
                                whileHover={{ rotate: 360, scale: 1.1 }}
                                transition={{ duration: 0.8 }}
                            >
                                <ShieldCheck className="h-8 w-8 text-[#b19763]" />
                            </motion.div>
                        </motion.div>
                        
                        <motion.h3 
                            className="text-2xl md:text-3xl font-bold text-[#062767] mb-4"
                            initial={{ y: 20, opacity: 0 }}
                            whileInView={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.5 }}
                            viewport={{ once: true }}
                        >
                            A Reliable Partner in Data Protection
                        </motion.h3>
                        
                        <motion.p 
                            className="text-black mb-8 max-w-3xl mx-auto text-lg leading-relaxed"
                            initial={{ y: 20, opacity: 0 }}
                            whileInView={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.3, duration: 0.5 }}
                            viewport={{ once: true }}
                        >
                            With a proven system that meets global expectations and regional data protection laws, Backsure Global Support ensures your information is safe, confidential, and always handled with integrity-no matter the scale or frequency of your operations.
                        </motion.p>
                        
                        <motion.div
                            initial={{ y: 20, opacity: 0 }}
                            whileInView={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.4, duration: 0.5 }}
                            viewport={{ once: true }}
                        >
                            <motion.button 
                                className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-4 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2 text-lg"
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.98 }}
                                transition={{ duration: 0.2 }}
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <ClipboardCheck className="h-5 w-5" />
                                Contact Us Today
                            </motion.button>
                        </motion.div>
                    </motion.div>
                </div>
        </section>
    );
}