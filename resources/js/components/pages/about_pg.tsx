import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Globe,
    Target,
    Rocket,
    Users,
    Shield,
    Handshake,
    Cpu,
    Zap,
    HeartHandshake,
    GitPullRequestArrow,
    CheckCircle,
    Info,
    Building,
    ArrowRight,
    Scale
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface Feature {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient?: string;
}

const purposeItems: Feature[] = [
    {
        icon: Globe,
        title: 'Core Purpose',
        description: 'Powering businesses, to create a thriving global community',
    },
    {
        icon: Target,
        title: 'Vision',
        description: 'To connect the business world to skilled people globally',
    },
    {
        icon: Rocket,
        title: 'Mission',
        description: 'To be the partner of choice for corporate growth, by enabling businesses to leverage the best global resources at the right price',
    }
];

const pillars: Feature[] = [
    {
        icon: GitPullRequestArrow,
        title: 'Continuous Improvement',
        description: 'We act and operate as a true reflection of our clients while promoting continuous improvement.',
    },
    {
        icon: Cpu,
        title: 'Unique Solutions',
        description: 'We deliver unique solutions to exceed expectations and our clients strategic objectives.',
    },
    {
        icon: Zap,
        title: 'Operational Stability',
        description: 'We maintain superior facilities and infrastructure to ensure operational stability and seamless delivery.',
    },
    {
        icon: Shield,
        title: 'Brand Protection',
        description: 'We protect our clients brands by employing industry best practices and control mechanisms for compliance.',
    }
];

const benefits: Feature[] = [
    {
        icon: Users,
        title: 'Industry-Ready Teams',
        description: 'We train our teams in industry tools, workflows, and compliance standards - whether you\'re in tech, healthcare, real estate, education, or beyond.',
    },
    {
        icon: Handshake,
        title: 'Flexible Engagement Models',
        description: 'Choose what fits your business - project-based, dedicated resources, or monthly subscription support.',
    },
    {
        icon: Cpu,
        title: 'Seamless Integration',
        description: 'Our backend teams blend into your operations, collaborating via your systems and CRMs as if they were part of your in-house team.',
    },
    {
        icon: HeartHandshake,
        title: 'Smart Processes + Human Touch',
        description: 'We use tech where it matters and people where it counts. Our goal? Efficiency with empathy.',
    }
];

export function AboutPage() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <Building className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Our Story
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">About Backsure Global Support</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Empowering Businesses with Strategic Support Solutions
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                                    <CheckCircle className="h-5 w-5 text-[#b19763] mr-2" />
                                    <span className="text-white font-medium">Trusted • Reliable • Innovative</span>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    <div className="max-w-4xl mx-auto">
                        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                            At Backsure Global Support, we're more than just a business services provider. We're a strategic partner dedicated to helping companies scale efficiently, optimize operations, and achieve sustainable growth. With our global expertise and tailored solutions, we empower businesses to focus on their core competencies while we handle the rest.
                        </p>
                    </div>

                    {/* Purpose Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Target className="h-7 w-7 text-[#b19763]" />
                            Our Purpose & Vision
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-3 gap-6">
                            {purposeItems.map((item, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <item.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{item.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Core Pillars Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Shield className="h-7 w-7 text-[#b19763]" />
                            Our Core Pillars
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {pillars.map((item, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <item.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{item.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Benefits Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <HeartHandshake className="h-7 w-7 text-[#b19763]" />
                            Why Choose Us
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {benefits.map((item, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <item.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{item.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Ready to Transform Your Business Operations?
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                Partner with us to optimize your processes, reduce costs, and accelerate growth with our tailored support solutions.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <Users className="h-5 w-5" />
                                Connect With Our Team
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}

interface FeatureCardProps {
    feature: Feature;
}

function FeatureCard({ feature }: FeatureCardProps) {
    const Icon = feature.icon;
    
    return (
        <Card className="text-center group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-blue-200 h-full">
            <CardHeader>
                <div className={`w-16 h-16 bg-gradient-to-br ${feature.gradient} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                    <Icon className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-xl mb-4 text-gray-900">{feature.title}</CardTitle>
                <CardDescription className="text-gray-600 leading-relaxed">
                    {feature.description}
                </CardDescription>
            </CardHeader>
        </Card>
    );
}