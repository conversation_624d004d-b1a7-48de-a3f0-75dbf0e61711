import { Link } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Layers,
    <PERSON><PERSON><PERSON>,
    ArrowRight
} from 'lucide-react';

export function FinanceWhy() {
    return (
        <section className="py-16 bg-white overflow-hidden">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">

                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                        Why It Matters
                    </h2>
                    <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                        Numbers Made Simple. <span className="text-blue-600">Finances Done Right.</span>
                        Let us manage your finance and accounting while you focus on business growth with confidence.
                    </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8 mb-16">
                    {/* Accuracy & Transparency */}
                    <div className="bg-gray-50 p-8 rounded-xl border border-gray-200">
                        <div className="flex items-center mb-4">
                            <div className="p-3 bg-blue-100 rounded-lg mr-4">
                                <Target className="h-6 w-6 text-blue-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900">Accuracy & Transparency</h3>
                        </div>
                        <p className="text-gray-600">
                            Error-free financials with real-time data access for informed decision-making.
                        </p>
                    </div>

                    {/* Regulatory Compliance */}
                    <div className="bg-gray-50 p-8 rounded-xl border border-gray-200">
                        <div className="flex items-center mb-4">
                            <div className="p-3 bg-green-100 rounded-lg mr-4">
                                <ShieldCheck className="h-6 w-6 text-green-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900">Regulatory Compliance</h3>
                        </div>
                        <p className="text-gray-600">
                            Stay aligned with UAE/GCC tax laws without last-minute surprises.
                        </p>
                    </div>

                    {/* Scalable Support */}
                    <div className="bg-gray-50 p-8 rounded-xl border border-gray-200">
                        <div className="flex items-center mb-4">
                            <div className="p-3 bg-purple-100 rounded-lg mr-4">
                                <Layers className="h-6 w-6 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-900">Scalable Support</h3>
                        </div>
                        <p className="text-gray-600">
                            Services that grow with you from startup to enterprise.
                        </p>
                    </div>
                </div>

                <div className="text-center">
                    <Button 
                        size="lg" 
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all" 
                        asChild
                    >
                        <Link href="/contact">
                            Request a Consultation
                            <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                    </Button>
                </div>
            </div>
        </section>
    );
}