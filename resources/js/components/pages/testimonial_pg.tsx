import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Star,
  Quote,
  CheckCircle,
  MessageSquare,
  Building,
  User,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface Testimonial {
  id: number;
  name: string;
  position: string;
  company: string;
  content: string;
  rating: number;
  date: string;
  industry: string;
  image: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON>",
    position: "Operations Director",
    company: "Global Insurance Partners",
    content: "Backsure Global Support has transformed our back-office operations. Their dedicated team seamlessly integrated with our processes, reducing our operational costs by 40% while improving accuracy and turnaround times. Their attention to detail and proactive approach has made them an invaluable extension of our team.",
    rating: 5,
    date: "March 2023",
    industry: "Insurance",
    image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  {
    id: 2,
    name: "<PERSON>",
    position: "CEO",
    company: "TechFlow Solutions",
    content: "Working with Backsure Global Support has been a game-changer for our startup. Their flexible support model allowed us to scale our customer service operations quickly without the overhead of hiring full-time staff. The team is responsive, professional, and truly cares about our success.",
    rating: 5,
    date: "January 2023",
    industry: "Technology",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  {
    id: 3,
    name: "Emily Rodriguez",
    position: "HR Manager",
    company: "Meridian Healthcare",
    content: "The dedicated team from Backsure Global Support has revolutionized our administrative processes. Their attention to detail in managing our documentation and data entry needs has reduced errors by 60% and freed up our internal team to focus on strategic initiatives. Their professionalism and reliability make them a trusted partner.",
    rating: 4,
    date: "May 2023",
    industry: "Healthcare",
    image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  {
    id: 4,
    name: "David Thompson",
    position: "Finance Director",
    company: "Westfield Properties",
    content: "We've been working with Backsure Global Support for over two years, and they've consistently delivered exceptional service. Their team has mastered our complex property management processes and provides reliable support that has improved our operational efficiency by 35%. Their ability to adapt to our changing needs has been particularly valuable.",
    rating: 5,
    date: "February 2023",
    industry: "Real Estate",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  {
    id: 5,
    name: "Priya Patel",
    position: "Operations Manager",
    company: "Nexus Financial Services",
    content: "Backsure Global Support has been instrumental in streamlining our customer onboarding process. Their dedicated team handles our documentation and verification processes with precision and care. The quality of their work and their commitment to meeting our standards has exceeded our expectations.",
    rating: 5,
    date: "April 2023",
    industry: "Financial Services",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  },
  {
    id: 6,
    name: "Robert Wilson",
    position: "CTO",
    company: "Elevate Digital",
    content: "The technical support team from Backsure Global Support has been exceptional. They quickly learned our systems and now provide 24/7 support that has significantly improved our response times and customer satisfaction. Their proactive approach to identifying and resolving issues has been particularly impressive.",
    rating: 4,
    date: "June 2023",
    industry: "Technology",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
  }
];

export function TestimonialPage() {
  const [isVisible, setIsVisible] = useState(false);
  const [filter, setFilter] = useState<string>('All');
  const [selectedTestimonial, setSelectedTestimonial] = useState<Testimonial | null>(null);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const industries = ['All', ...Array.from(new Set(testimonials.map(item => item.industry)))];
  const filteredTestimonials = filter === 'All' ? testimonials : testimonials.filter(item => item.industry === filter);

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star 
        key={i} 
        className={`h-4 w-4 ${i < rating ? 'text-[#b19763] fill-[#b19763]' : 'text-gray-300'}`} 
      />
    ));
  };

  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section with Parallax Effect */}
        <div className="relative overflow-hidden rounded-2xl mb-16">
          <div className="absolute inset-0 bg-cover bg-center" 
               style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80)', 
                      filter: 'brightness(0.4)' }}>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
          <motion.div 
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            variants={fadeIn}
            transition={{ duration: 0.6 }}
            className="relative py-20 px-6 text-center">
            <div className="max-w-3xl mx-auto">
              <motion.div 
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                <span className="text-white/90 text-sm font-medium flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2 text-[#b19763]" />
                  Client Success Stories
                </span>
              </motion.div>
              <h2 className="text-5xl font-bold text-white mb-6">What Our Clients Say</h2>
              <p className="text-xl text-white/90 mb-8">
                Discover how our dedicated support solutions have helped businesses like yours
              </p>
              <motion.div 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="flex justify-center">
                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                  <CheckCircle className="h-5 w-5 text-[#b19763] mr-2" />
                  <span className="text-white font-medium">Trusted • Reliable • Effective</span>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Main Content with Testimonials */}
        <motion.div 
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          variants={fadeIn}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
          
          {/* Industry Filter */}
          <div className="mb-10">
            <h3 className="text-xl font-semibold text-[#062767] mb-6">Filter by Industry</h3>
            <div className="flex flex-wrap gap-3">
              {industries.map((industry, index) => (
                <button
                  key={index}
                  onClick={() => setFilter(industry)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${filter === industry ? 'bg-[#062767] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                >
                  {industry}
                </button>
              ))}
            </div>
          </div>

          {/* Featured Testimonial */}
          {selectedTestimonial && (
            <motion.div 
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.3 }}
              className="mb-12 bg-[#f8f5ee] rounded-xl p-8 relative overflow-hidden"
            >
              <div className="absolute top-0 right-0 w-32 h-32 bg-[#b19763]/10 rounded-bl-full"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-[#062767]/5 rounded-tr-full"></div>
              
              <Quote className="h-12 w-12 text-[#b19763]/20 mb-6" />
              
              <div className="flex flex-col md:flex-row gap-8">
                <div className="md:w-1/3">
                  <div className="aspect-square rounded-xl overflow-hidden mb-4 shadow-lg">
                    <img 
                      src={selectedTestimonial.image} 
                      alt={selectedTestimonial.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-xl font-semibold text-[#062767]">{selectedTestimonial.name}</h4>
                    <p className="text-gray-600">{selectedTestimonial.position}</p>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-[#b19763]" />
                      <p className="font-medium">{selectedTestimonial.company}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex">{renderStars(selectedTestimonial.rating)}</div>
                    </div>
                  </div>
                </div>
                
                <div className="md:w-2/3">
                  <p className="text-lg text-gray-700 italic leading-relaxed mb-6">
                    {selectedTestimonial.content}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      {selectedTestimonial.date}
                    </div>
                    <button 
                      onClick={() => setSelectedTestimonial(null)}
                      className="text-[#062767] font-medium hover:text-[#0a3a8a] inline-flex items-center gap-1 transition-colors duration-300"
                    >
                      View All Testimonials
                      <ArrowRight className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Testimonial Grid */}
          {!selectedTestimonial && (
            <motion.div 
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTestimonials.map((testimonial) => (
                <motion.div 
                  key={testimonial.id}
                  variants={fadeIn}
                  className="group cursor-pointer"
                  onClick={() => setSelectedTestimonial(testimonial)}
                >
                  <Card className="h-full border border-gray-200 hover:border-[#b19763]/30 hover:shadow-md transition-all duration-300 overflow-hidden">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex -space-x-2">
                          <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-sm">
                            <img 
                              src={testimonial.image} 
                              alt={testimonial.name} 
                              className="w-full h-full object-cover"
                            />
                          </div>
                        </div>
                        <div className="flex">
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <p className="text-gray-700 line-clamp-4 group-hover:text-gray-900 transition-colors duration-300">
                          {testimonial.content}
                        </p>
                      </div>
                      
                      <div className="pt-4 border-t border-gray-100">
                        <h4 className="font-semibold text-[#062767]">{testimonial.name}</h4>
                        <p className="text-sm text-gray-600">{testimonial.position}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Building className="h-3 w-3 text-[#b19763]" />
                          <p className="text-sm">{testimonial.company}</p>
                        </div>
                      </div>
                      
                      <div className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="p-2 bg-[#062767] rounded-full text-white">
                          <ArrowRight className="h-4 w-4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* Closing CTA with Gradient and Animation */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="mt-16 relative overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
            <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
            <div className="relative p-10 text-center">
              <h3 className="text-3xl font-bold text-white mb-4">
                Ready to Experience the Difference?
              </h3>
              <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                Join our satisfied clients and discover how our dedicated support solutions can transform your business operations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button 
                  className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                  onClick={() => window.location.href = "/contact"}
                >
                  <User className="h-5 w-5" />
                  Schedule a Consultation
                </button>
                <button 
                  className="bg-[#b19763] hover:bg-[#c9ad7a] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-xl"
                  onClick={() => window.location.href = "/services"}
                >
                  <ArrowRight className="h-5 w-5" />
                  Explore Our Services
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}