import { BookOpen, Mail, Calendar, ArrowRight, ChevronLeft, ChevronRight, Search, Tag, Filter, Clock, User } from 'lucide-react';
import { useState } from 'react';

export function BlogPage() {
    const [activeCategory, setActiveCategory] = useState('All');
    const [searchQuery, setSearchQuery] = useState('');

    const categories = [
        'All',
        'Outsourcing Tips',
        'HR Management',
        'Finance & Accounting',
        'Compliance & Admin',
        'Business Growth'
    ];

    const articles = [
        {
            title: "5 Ways Outsourcing Can Accelerate Your Business Growth",
            category: "Outsourcing Tips",
            date: "April 15, 2025",
            author: "<PERSON>",
            readTime: "5 min read",
            featured: true,
            image: "https://picsum.photos/800/600?random=1",
            excerpt: "Learn how strategic outsourcing can help you scale faster, reduce costs, and focus on your core business strengths in today's competitive landscape. Discover the key benefits that make outsourcing a game-changer for businesses of all sizes."
        },
        {
            title: "Building Effective Remote Teams: Best Practices",
            category: "HR Management",
            date: "April 12, 2025",
            author: "<PERSON>",
            readTime: "4 min read",
            featured: true,
            image: "https://picsum.photos/800/600?random=2",
            excerpt: "Discover proven strategies for managing remote teams effectively and maintaining strong team culture across borders."
        },
        {
            title: "Streamlining Financial Operations: Key Strategies for SMEs",
            category: "Finance & Accounting",
            date: "April 8, 2025",
            author: "Priya Sharma",
            readTime: "6 min read",
            featured: false,
            image: "https://picsum.photos/800/600?random=3",
            excerpt: "Learn practical approaches to optimize your financial processes, reduce costs, and improve financial visibility for better decision-making."
        },
        {
            title: "Understanding UAE Corporate Tax: A Guide for Businesses",
            category: "Compliance & Admin",
            date: "April 5, 2025",
            author: "Ahmed Al-Mansour",
            readTime: "7 min read",
            featured: false,
            image: "https://picsum.photos/800/600?random=4",
            excerpt: "Navigate the complexities of UAE's corporate tax system with this comprehensive guide for business owners and finance teams."
        },
        {
            title: "Digital Transformation: Adapting Your Business for Future Success",
            category: "Business Growth",
            date: "March 30, 2025",
            author: "Jessica Williams",
            readTime: "5 min read",
            featured: false,
            image: "https://picsum.photos/800/600?random=5",
            excerpt: "Explore the essential steps to successfully implement digital transformation in your business and stay ahead of the competition."
        },
        {
            title: "How to Choose the Right Outsourcing Partner for Your Business",
            category: "Outsourcing Tips",
            date: "March 25, 2025",
            author: "David Thompson",
            readTime: "4 min read",
            featured: false,
            image: "https://picsum.photos/800/600?random=6",
            excerpt: "Understand the key factors to consider when selecting an outsourcing partner that aligns with your business goals and values."
        }
    ];

    const filteredArticles = activeCategory === 'All'
        ? articles
        : articles.filter(article => article.category === activeCategory);

    const featuredArticles = articles.filter(article => article.featured);

    // Filter articles based on search query if provided
    const searchedArticles = searchQuery.trim() === ''
        ? filteredArticles
        : filteredArticles.filter(article =>
            article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
            article.category.toLowerCase().includes(searchQuery.toLowerCase())
        );

    const handleSearch = (e) => {
        e.preventDefault();
        // Search functionality can be expanded here
    };

    return (
        <section className="py-16 bg-gray-50">
            <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                {/* Page Header */}
                <div className="bg-gradient-to-r from-[#b19763] to-[#d4b77d] py-20 px-6 mb-16 rounded-xl shadow-lg overflow-hidden relative">
                    <div className="absolute inset-0 opacity-10 bg-pattern"></div>
                    <div className="relative z-10 mx-auto max-w-3xl text-center">
                        <h2 className="mb-6 text-5xl font-bold leading-tight text-white">Insights & Resources</h2>
                        <p className="mb-8 text-xl text-white opacity-90">
                            Explore the latest insights, industry trends, and expert advice to help your business thrive in today's competitive market.
                        </p>
                    </div>
                </div>

                {/* Categories Filter */}
                <div className="flex flex-wrap gap-3 justify-center items-center mb-8">
                    <div className="flex items-center mr-2 text-[#062767]">
                        <Filter className="mr-2 w-5 h-5" />
                        <span className="font-medium">Filter by:</span>
                    </div>
                    {categories.map((category) => (
                        <button
                            key={category}
                            onClick={() => setActiveCategory(category)}
                            className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeCategory === category
                                ? 'bg-[#062767] text-white shadow-md'
                                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'}`}
                        >
                            {category}
                        </button>
                    ))}
                </div>

                {/* Search Box - Moved below filters */}
                <div className="mb-12">
                    <form onSubmit={handleSearch} className="relative mx-auto max-w-xl">
                        <div className="flex relative items-center">
                            <input
                                type="text"
                                placeholder="Search articles..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full py-4 px-6 pl-12 rounded-full shadow-xl focus:outline-none focus:ring-4 focus:ring-[#062767]/30 text-gray-700 border-2 border-[#062767]/10 text-lg bg-white"
                            />
                            <div className="absolute left-4 text-[#062767]">
                                <Search className="w-5 h-5" />
                            </div>
                            <button
                                type="submit"
                                className="absolute right-2 bg-[#062767] hover:bg-[#062767]/90 text-white font-medium py-2 px-6 rounded-full transition-all duration-200 flex items-center gap-2"
                            >
                                Search
                            </button>
                        </div>
                    </form>
                </div>

                {/* Featured Articles */}
                {activeCategory === 'All' && searchQuery.trim() === '' && (
                    <div className="mb-16">
                        <h3 className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b border-gray-200 pb-4">
                            <BookOpen className="h-6 w-6 text-[#b19763]" />
                            Featured Articles
                        </h3>

                        <div className="grid gap-8 lg:grid-cols-2">
                            {featuredArticles.map((article, index) => (
                                <div key={index} className="flex overflow-hidden flex-col h-full bg-white rounded-xl border border-gray-100 shadow-md transition-all duration-300 hover:shadow-lg group">
                                    <div className="overflow-hidden relative h-64">
                                        <div className="absolute top-4 left-4 z-10 bg-[#b19763] text-white text-xs font-bold uppercase tracking-wider py-1 px-3 rounded-full">
                                            Featured
                                        </div>
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-[1]"></div>
                                        <img
                                            src={article.image || 'https://picsum.photos/800/600?random=featured'}
                                            alt={article.title}
                                            className="object-cover w-full h-full transition-transform duration-500 ease-out group-hover:scale-105"
                                        />
                                    </div>
                                    <div className="flex flex-col flex-grow p-6">
                                        <div className="flex items-center gap-3 text-sm text-[#b19763] mb-2 font-medium">
                                            <Tag className="w-4 h-4" />
                                            {article.category}
                                        </div>
                                        <h4 className="text-2xl font-bold text-[#062767] mb-4 group-hover:text-[#b19763] transition-colors duration-300">{article.title}</h4>
                                        <p className="flex-grow mb-6 text-gray-700">{article.excerpt}</p>
                                        <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                                            <div className="flex gap-2 items-center">
                                                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-[#062767]">
                                                    <User className="w-4 h-4" />
                                                </div>
                                                <span className="text-sm text-gray-600">{article.author}</span>
                                            </div>
                                            <div className="flex gap-4 items-center">
                                                <div className="flex gap-1 items-center text-sm text-gray-500">
                                                    <Calendar className="h-4 w-4 text-[#062767]" />
                                                    <span>{article.date}</span>
                                                </div>
                                                <div className="flex gap-1 items-center text-sm text-gray-500">
                                                    <Clock className="h-4 w-4 text-[#062767]" />
                                                    <span>{article.readTime}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* All Articles Grid */}
                <div className="mb-16">
                    <h3 className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b border-gray-200 pb-4">
                        <BookOpen className="h-6 w-6 text-[#b19763]" />
                        {searchQuery.trim() !== ''
                            ? `Search Results (${searchedArticles.length})`
                            : activeCategory === 'All'
                                ? 'Latest Articles'
                                : `${activeCategory} Articles`
                        }
                    </h3>

                    {searchedArticles.length === 0 && (
                        <div className="py-12 text-center bg-white rounded-xl shadow-sm">
                            <div className="mb-4 text-6xl">🔍</div>
                            <h4 className="text-xl font-bold text-[#062767] mb-2">No articles found</h4>
                            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
                        </div>
                    )}

                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        {searchedArticles.map((article, index) => (
                            <div key={index} className="flex overflow-hidden flex-col h-full bg-white rounded-xl border border-gray-100 shadow-md transition-all duration-300 hover:shadow-lg group">
                                <div className="overflow-hidden relative h-48">
                                    <img
                                        src={article.image || 'https://picsum.photos/800/600?random=default'}
                                        alt={article.title}
                                        className="object-cover w-full h-full transition-transform duration-500 ease-out group-hover:scale-105"
                                    />
                                </div>
                                <div className="flex flex-col flex-grow p-6">
                                    <div className="flex items-center gap-2 text-sm text-[#b19763] mb-2 font-medium">
                                        <Tag className="w-4 h-4" />
                                        {article.category}
                                    </div>
                                    <h4 className="text-xl font-bold text-[#062767] mb-3 group-hover:text-[#b19763] transition-colors duration-300">{article.title}</h4>
                                    <p className="flex-grow mb-5 text-gray-700">{article.excerpt}</p>
                                    <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                                        <div className="flex gap-1 items-center text-sm text-gray-500">
                                            <Calendar className="h-4 w-4 text-[#062767]" />
                                            <span>{article.date}</span>
                                        </div>
                                        <button className="text-[#b19763] hover:text-[#062767] font-medium flex items-center gap-1 transition-colors duration-200 group">
                                            Read More
                                            <ArrowRight className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Pagination */}
                {searchedArticles.length > 0 && (
                    <div className="flex gap-2 justify-center items-center mb-16">
                        <button className="p-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors duration-200 text-[#062767]">
                            <ChevronLeft className="w-5 h-5" />
                        </button>

                        <button className="w-10 h-10 rounded-md bg-[#062767] text-white font-medium">1</button>

                        {[2, 3, 4, 5].map((page) => (
                            <button key={page} className="w-10 h-10 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors duration-200 font-medium text-[#062767]">
                                {page}
                            </button>
                        ))}

                        <button className="p-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors duration-200 text-[#062767]">
                            <ChevronRight className="w-5 h-5" />
                        </button>
                    </div>
                )}

                {/* Newsletter Subscription */}
                <div className="bg-[#062767] p-10 rounded-xl shadow-lg mb-16 relative overflow-hidden">
                    <div className="absolute inset-0 opacity-5 bg-pattern"></div>
                    <div className="flex relative z-10 flex-col gap-8 justify-between items-center md:flex-row">
                        <div className="md:w-1/2">
                            <h3 className="mb-4 text-2xl font-bold text-white">
                                Subscribe to Our Newsletter
                            </h3>
                            <p className="mb-0 max-w-lg text-blue-100">
                                Stay updated with our latest insights, industry trends, and expert advice delivered directly to your inbox.
                            </p>
                        </div>
                        <div className="w-full md:w-1/2">
                            <div className="flex flex-col gap-3 sm:flex-row">
                                <input
                                    type="email"
                                    placeholder="Enter your email"
                                    className="flex-grow py-3 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#b19763]"
                                />
                                <button className="bg-[#b19763] hover:bg-[#c9b084] text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200">
                                    Subscribe
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Closing CTA */}
                <div className="bg-[#f8f5ee] p-10 rounded-xl shadow-md text-center">
                    <h3 className="text-2xl font-bold text-[#062767] mb-4">
                        Focus on Growth. We'll Handle the Rest.
                    </h3>
                    <p className="mx-auto mb-8 max-w-3xl text-gray-700">
                        Let BackSure Global Support take care of your administrative functions while you concentrate on what matters most—growing your business.
                    </p>
                    <button className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-200 inline-flex items-center gap-2 shadow-md hover:shadow-lg">
                        <Mail className="w-5 h-5" />
                        Get in Touch with Us
                    </button>
                </div>
            </div>
        </section>
    );
}
