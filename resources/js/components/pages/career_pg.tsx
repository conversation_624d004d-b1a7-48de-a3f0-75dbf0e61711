import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Briefcase,
    Building,
    Users,
    Heart,
    Clock,
    Coffee,
    Laptop,
    Zap,
    Award,
    CheckCircle,
    ArrowRight,
    Send,
    GraduationCap,
    Sparkles,
    Leaf,
    HeartHandshake
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface JobPosition {
    title: string;
    department: string;
    location: string;
    type: string;
    icon: React.ComponentType<{ className?: string }>;
}

interface Benefit {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

const openPositions: JobPosition[] = [
    {
        title: 'Customer Support Specialist',
        department: 'Customer Success',
        location: 'Remote',
        type: 'Full-time',
        icon: HeartHandshake
    },
    {
        title: 'Sales Development Representative',
        department: 'Sales',
        location: 'Remote',
        type: 'Full-time',
        icon: Briefcase
    },
    {
        title: 'Administrative Assistant',
        department: 'Operations',
        location: 'Remote',
        type: 'Full-time',
        icon: Coffee
    },
    {
        title: 'Data Entry Specialist',
        department: 'Operations',
        location: 'Remote',
        type: 'Full-time',
        icon: Laptop
    }
];

const benefits: Benefit[] = [
    {
        icon: GraduationCap,
        title: 'Continuous Learning',
        description: 'Regular training programs and skill development opportunities to help you grow professionally.'
    },
    {
        icon: Sparkles,
        title: 'Work-Life Balance',
        description: 'Flexible schedules and remote work options to ensure you can balance your personal and professional life.'
    },
    {
        icon: Leaf,
        title: 'Wellness Programs',
        description: 'Access to wellness initiatives, mental health resources, and regular team-building activities.'
    },
    {
        icon: Award,
        title: 'Recognition & Growth',
        description: 'Clear career paths, performance recognition, and opportunities for advancement within the company.'
    }
];

export function CareerPage() {
    const [isVisible, setIsVisible] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        position: '',
        message: ''
    });

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle form submission logic here
        console.log('Form submitted:', formData);
        // Reset form
        setFormData({
            name: '',
            email: '',
            position: '',
            message: ''
        });
        // Show success message
        alert('Your application has been submitted. We will contact you soon!');
    };

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <Briefcase className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Join Our Team
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">Careers at Backsure Global Support</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Build your career with a company that values growth, innovation, and excellence
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                                    <CheckCircle className="h-5 w-5 text-[#b19763] mr-2" />
                                    <span className="text-white font-medium">Innovative • Supportive • Collaborative</span>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    
                    {/* Introduction */}
                    <div className="max-w-4xl mx-auto mb-12">
                        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                            At Backsure Global Support, we're building a team of exceptional individuals who are passionate about delivering outstanding service to our clients. We offer a dynamic work environment where your skills and contributions are valued, recognized, and rewarded.
                        </p>
                    </div>

                    {/* Current Openings Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Briefcase className="h-7 w-7 text-[#b19763]" />
                            Current Openings
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {openPositions.map((position, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <position.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{position.title}</h4>
                                    </div>
                                    <div className="space-y-2 text-gray-600 group-hover:text-gray-700">
                                        <div className="flex items-center gap-2">
                                            <Building className="h-4 w-4 text-gray-400" />
                                            <span>{position.department}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Users className="h-4 w-4 text-gray-400" />
                                            <span>{position.location}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Clock className="h-4 w-4 text-gray-400" />
                                            <span>{position.type}</span>
                                        </div>
                                    </div>
                                    <div className="mt-4 pt-4 border-t border-gray-200">
                                        <button 
                                            className="text-[#062767] font-medium hover:text-[#0a3a8a] inline-flex items-center gap-1 transition-colors duration-300"
                                            onClick={() => document.getElementById('application-form')?.scrollIntoView({ behavior: 'smooth' })}
                                        >
                                            Apply Now
                                            <ArrowRight className="h-4 w-4" />
                                        </button>
                                    </div>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* What We Offer Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Heart className="h-7 w-7 text-[#b19763]" />
                            What We Offer
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {benefits.map((benefit, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <benefit.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{benefit.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {benefit.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Application Form Section */}
                    <div id="application-form" className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Send className="h-7 w-7 text-[#b19763]" />
                            How to Apply
                        </motion.h3>

                        <motion.div 
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.6, duration: 0.5 }}
                            className="max-w-3xl mx-auto bg-gray-50 p-8 rounded-xl border border-gray-200">
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div>
                                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                        <input 
                                            type="text" 
                                            id="name" 
                                            name="name" 
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#b19763] focus:border-transparent transition-all duration-300"
                                        />
                                    </div>
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                        <input 
                                            type="email" 
                                            id="email" 
                                            name="email" 
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#b19763] focus:border-transparent transition-all duration-300"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">Position You're Applying For</label>
                                    <select 
                                        id="position" 
                                        name="position" 
                                        value={formData.position}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#b19763] focus:border-transparent transition-all duration-300"
                                    >
                                        <option value="">Select a position</option>
                                        {openPositions.map((position, index) => (
                                            <option key={index} value={position.title}>{position.title}</option>
                                        ))}
                                        <option value="Other">Other/Not Listed</option>
                                    </select>
                                </div>
                                <div>
                                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">Why do you want to join our team?</label>
                                    <textarea 
                                        id="message" 
                                        name="message" 
                                        value={formData.message}
                                        onChange={handleInputChange}
                                        rows={4}
                                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#b19763] focus:border-transparent transition-all duration-300"
                                    ></textarea>
                                </div>
                                <div className="flex justify-center">
                                    <button 
                                        type="submit" 
                                        className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                    >
                                        <Send className="h-5 w-5" />
                                        Submit Application
                                    </button>
                                </div>
                            </form>
                        </motion.div>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Don't See a Position That Fits?
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                We're always looking for talented individuals to join our team. Send us your resume and let us know how you can contribute to our success.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "mailto:<EMAIL>"}
                            >
                                <Send className="h-5 w-5" />
                                Send Your Resume
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}