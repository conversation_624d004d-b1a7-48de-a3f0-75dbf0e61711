import { BookOpen, Mail, Calendar, ArrowRight, ChevronLeft, ChevronRight, Scale } from 'lucide-react';

export function TermsPage() {
    return (
        <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Page Header */}
				<div className="bg-[#b19763] py-16 px-4 mb-12 rounded-lg">
				    <div className="max-w-3xl mx-auto text-center">
				        <h2 className="text-4xl font-bold text-white mb-4">Terms & Conditions</h2>
				        <p className="text-xl text-white opacity-90">
				            Please read our terms and conditions carefully.
				        </p>
				    </div>
				</div>     
				
				{/* Terms Content */}
				<div className="bg-white rounded-lg shadow-md p-8 mb-12">
				    <p className="text-lg text-gray-700 mb-6">
				        These terms and conditions outline the rules and regulations for the use of Backsure Global Support's services.
				    </p>
				    <p className="text-lg text-gray-700 mb-6">
				        By accessing our services, we assume you accept these terms and conditions in full. Do not continue to use our services if you do not accept all of the terms and conditions stated on this page.
				    </p>
				    <h3 className="text-xl font-semibold text-[#062767] mt-8 mb-4">Service Agreement</h3>
				    <p className="text-lg text-gray-700 mb-6">
				        The content of this page is for your general information and use only. It is subject to change without notice.
				    </p>
				    <h3 className="text-xl font-semibold text-[#062767] mt-8 mb-4">Privacy Policy</h3>
				    <p className="text-lg text-gray-700 mb-6">
				        Your use of any information or materials on this website is entirely at your own risk, for which we shall not be liable. It shall be your own responsibility to ensure that any products, services or information available through this website meet your specific requirements.
				    </p>
				</div>
				
                {/* Closing CTA */}
                <div className="bg-[#f8f5ee] p-8 rounded-lg text-center">
                    <h3 className="text-2xl font-bold text-[#062767] mb-4">
                        Have Questions About Our Terms?
                    </h3>
                    <p className="text-black mb-6 max-w-3xl mx-auto">
                        Contact our team for clarification on any aspect of our terms and conditions.
                    </p>
                    <button className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2"
                    	onClick={() => window.location.href = "/contact"}
                    >
                        <Mail className="h-5 w-5" />
                        Contact Us
                    </button>
                </div>				           
            </div>
        </section>
    );
}