import { Phone, Mail, MapPin, Calendar, ClipboardList, User, Shield, Briefcase, Zap } from 'lucide-react';
import { useState } from 'react';

export function ContactPage() {
  const [activeTab, setActiveTab] = useState('contact');
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTimeZone, setSelectedTimeZone] = useState('dubai');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [serviceType, setServiceType] = useState('');

  const handleServiceToggle = (service: string) => {
    if (selectedServices.includes(service)) {
      setSelectedServices(selectedServices.filter(s => s !== service));
    } else {
      setSelectedServices([...selectedServices, service]);
    }
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Page Header */}

		<div className="bg-[#b19763] py-16 px-4 mb-12 rounded-lg">
		    <div className="max-w-3xl mx-auto text-center">
		        <h2 className="text-4xl font-bold text-white mb-4">Contact Us</h2>
		        <p className="text-xl text-white opacity-90">
		            Let's discuss how our professional business support solutions can help your business scale smarter and grow faster
		        </p>
		    </div>
		</div>        

        <div className="grid md:grid-cols-2 gap-12">
          {/* Contact Methods */}
          <div className="space-y-8">
            {/* Phone */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-start gap-4">
                <div className="bg-[#f8f5ee] p-3 rounded-full">
                  <Phone className="h-6 w-6 text-[#b19763]" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-[#062767] mb-1">Call Us</h3>
                  <p className="text-black mb-3">Speak directly with our business support specialists</p>
                  <a href="tel:+971524419445" className="text-[#b19763] hover:text-[#062767] font-medium transition-colors duration-200">
                    +971 52 441 9445
                  </a>
                </div>
              </div>
            </div>

            {/* Email */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-start gap-4">
                <div className="bg-[#f8f5ee] p-3 rounded-full">
                  <Mail className="h-6 w-6 text-[#b19763]" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-[#062767] mb-1">Email Us</h3>
                  <p className="text-black mb-3">Our business support team will respond within 24 hours</p>
                  <a href="mailto:<EMAIL>" className="text-[#b19763] hover:text-[#062767] font-medium transition-colors duration-200">
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>

            {/* Locations */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
              <div className="flex items-start gap-4">
                <div className="bg-[#f8f5ee] p-3 rounded-full">
                  <MapPin className="h-6 w-6 text-[#b19763]" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-[#062767] mb-4">Our Locations</h3>
                  
                  <div className="space-y-4">
                    {/* UAE Location */}
                    <div>
                      <h4 className="font-semibold text-[#062767]">United Arab Emirates</h4>
                      <p className="text-black">Paradise Building, Barsha Heights</p>
                      <p className="text-black">Dubai</p>
                    </div>
                    
                    {/* India Location */}
                    <div>
                      <h4 className="font-semibold text-[#062767]">India</h4>
                      <p className="text-black">Corporate Court, #108 Infantry Road</p>
                      <p className="text-black">Bangalore - 560 001</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Forms with Tabs */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <div className="flex border-b border-gray-200 mb-6">
              <button
                onClick={() => setActiveTab('contact')}
                className={`py-2 px-4 font-medium text-sm ${activeTab === 'contact' ? 'text-[#b19763] border-b-2 border-[#b19763]' : 'text-[#062767] hover:text-[#b19763]'}`}
              >
                General Contact
              </button>
              <button
                onClick={() => setActiveTab('consultation')}
                className={`py-2 px-4 font-medium text-sm ${activeTab === 'consultation' ? 'text-[#b19763] border-b-2 border-[#b19763]' : 'text-[#062767] hover:text-[#b19763]'}`}
              >
                Schedule Consultation
              </button>
              <button
                onClick={() => setActiveTab('service')}
                className={`py-2 px-4 font-medium text-sm ${activeTab === 'service' ? 'text-[#b19763] border-b-2 border-[#b19763]' : 'text-[#062767] hover:text-[#b19763]'}`}
              >
                Service Intake
              </button>
            </div>

            {/* General Contact Form */}
            {activeTab === 'contact' && (
              <div>
                <h3 className="text-2xl font-bold text-[#062767] mb-6">Get in Touch</h3>
                <p className="text-gray-600 mb-4">Tell us about your business support needs and how we can help with your insurance, finance, or enterprise operations.</p>
                <form className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-[#062767] mb-1">Name</label>
                    <input 
                      type="text" 
                      id="name" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your name"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-[#062767] mb-1">Email</label>
                    <input 
                      type="email" 
                      id="email" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your email"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-[#062767] mb-1">Subject</label>
                    <input 
                      type="text" 
                      id="subject" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Subject"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-[#062767] mb-1">Message</label>
                    <textarea 
                      id="message" 
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your message"
                    ></textarea>
                  </div>
                  
                  <button 
                    type="submit"
                    className="w-full bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-6 rounded-md transition-colors duration-200"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            )}

            {/* Consultation Form */}
            {activeTab === 'consultation' && (
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Calendar className="h-5 w-5 text-[#b19763]" />
                  <h3 className="text-2xl font-bold text-[#062767]">Schedule a Business Consultation</h3>
                </div>
                <p className="text-black mb-6">Book a time with our business support specialists to discuss how our professional services can help streamline your operations and drive growth.</p>
                
                <form className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-[#062767] mb-1">Select a date and time</label>
                    <input 
                      type="date" 
                      value={selectedDate}
                      onChange={(e) => setSelectedDate(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#062767] mb-2">Time Zone</label>
                    <div className="space-y-2">
                      {['dubai', 'new york', 'india', 'london'].map(zone => (
                        <div key={zone} className="flex items-center">
                          <input
                            type="radio"
                            id={zone}
                            name="timezone"
                            checked={selectedTimeZone === zone}
                            onChange={() => setSelectedTimeZone(zone)}
                            className="h-4 w-4 text-[#b19763] focus:ring-[#b19763] border-gray-300"
                          />
                          <label htmlFor={zone} className="ml-2 block text-sm text-black capitalize">
                            {zone}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#062767] mb-2">Available Time Slots</label>
                    <div className="grid grid-cols-2 gap-2">
                      {['10-11am', '11am-12pm', '1-2pm', '2-3pm', '3-4pm'].map(slot => (
                        <div key={slot} className="flex items-center">
                          <input
                            type="radio"
                            id={slot}
                            name="timeslot"
                            checked={selectedTimeSlot === slot}
                            onChange={() => setSelectedTimeSlot(slot)}
                            className="h-4 w-4 text-[#b19763] focus:ring-[#b19763] border-gray-300"
                          />
                          <label htmlFor={slot} className="ml-2 block text-sm text-black">
                            {slot}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2 mb-4">
                      <User className="h-5 w-5 text-[#b19763]" />
                      <h4 className="text-lg font-semibold text-[#062767]">Your Information</h4>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label htmlFor="consult-name" className="block text-sm font-medium text-[#062767] mb-1">Name</label>
                        <input 
                          type="text" 
                          id="consult-name" 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                          placeholder="Your name"
                        />
                      </div>

                      <div>
                        <label htmlFor="consult-email" className="block text-sm font-medium text-[#062767] mb-1">Email</label>
                        <input 
                          type="email" 
                          id="consult-email" 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                          placeholder="Your email"
                        />
                      </div>

                      <div>
                        <label htmlFor="consult-phone" className="block text-sm font-medium text-[#062767] mb-1">Phone Number</label>
                        <input 
                          type="tel" 
                          id="consult-phone" 
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                          placeholder="Your phone number"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-[#062767] mb-2">Services Interested In</label>
                        <div className="space-y-2">
                          {['Dedicated Team', 'On-Demand Service Support', 'Business Care Plans'].map(service => (
                            <div key={service} className="flex items-center">
                              <input
                                type="checkbox"
                                id={service.toLowerCase().replace(/\s+/g, '-')}
                                checked={selectedServices.includes(service)}
                                onChange={() => handleServiceToggle(service)}
                                className="h-4 w-4 text-[#b19763] focus:ring-[#b19763] border-gray-300 rounded"
                              />
                              <label htmlFor={service.toLowerCase().replace(/\s+/g, '-')} className="ml-2 block text-sm text-black">
                                {service}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <label htmlFor="purpose" className="block text-sm font-medium text-[#062767] mb-1">Meeting Purpose</label>
                        <textarea 
                          id="purpose" 
                          rows={3}
                          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                          placeholder="Briefly describe what you'd like to discuss"
                        ></textarea>
                      </div>
                    </div>
                  </div>

                  <button 
                    type="submit"
                    className="w-full bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-6 rounded-md transition-colors duration-200 mt-6"
                  >
                    Schedule Consultation
                  </button>
                </form>
              </div>
            )}

            {/* Service Intake Form */}
            {activeTab === 'service' && (
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <ClipboardList className="h-5 w-5 text-[#b19763]" />
                  <h3 className="text-2xl font-bold text-[#062767]">Business Support Intake Form</h3>
                </div>
                <p className="text-black mb-6">Help us understand your specific business support needs. Fill out this brief intake form so we can tailor our professional solutions for your insurance, finance, or enterprise operations.</p>
                
                <form className="space-y-4">
                  <div>
                    <label htmlFor="service-name" className="block text-sm font-medium text-[#062767] mb-1">Name</label>
                    <input 
                      type="text" 
                      id="service-name" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your name"
                    />
                  </div>

                  <div>
                    <label htmlFor="service-email" className="block text-sm font-medium text-[#062767] mb-1">Email</label>
                    <input 
                      type="email" 
                      id="service-email" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your email"
                    />
                  </div>

                  <div>
                    <label htmlFor="service-phone" className="block text-sm font-medium text-[#062767] mb-1">Phone Number</label>
                    <input 
                      type="tel" 
                      id="service-phone" 
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Your phone number"
                    />
                  </div>

                  <div>
                    <label htmlFor="service-type" className="block text-sm font-medium text-[#062767] mb-1">Service Type</label>
                    <select
                      id="service-type"
                      value={serviceType}
                      onChange={(e) => setServiceType(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                    >
                      <option value="">Select a service type</option>
                      <option value="dedicated team">Dedicated Team</option>
                      <option value="on-demand support">On-Demand Service Support</option>
                      <option value="business care">Business Care Plans</option>
                      <option value="hr management">HR Management</option>
                      <option value="finance accounting">Finance & Accounting</option>
                      <option value="compliance">Compliance & Business Administration</option>
                      <option value="insurance">Insurance Support</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="business-industry" className="block text-sm font-medium text-[#062767] mb-1">Business Industry</label>
                    <select
                      id="business-industry"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                    >
                      <option value="">Select your industry</option>
                      <option value="insurance">Insurance</option>
                      <option value="banking">Banking & Finance</option>
                      <option value="financial services">Financial Services</option>
                      <option value="enterprise">Enterprise Operations</option>
                      <option value="startups">Startups & SMEs</option>
                      <option value="international business">International Business</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="timeline" className="block text-sm font-medium text-[#062767] mb-1">Implementation Timeline</label>
                    <select
                      id="timeline"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                    >
                      <option value="">Select your timeline</option>
                      <option value="immediately">Immediately</option>
                      <option value="within 1 month">Within 1 month</option>
                      <option value="1-3 months">1-3 months</option>
                      <option value="2-6 months">2-6 months</option>
                      <option value="more than 6 months">More than 6 months</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="comments" className="block text-sm font-medium text-[#062767] mb-1">Additional Comments</label>
                    <textarea 
                      id="comments" 
                      rows={4}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#b19763] focus:border-[#b19763] outline-none transition"
                      placeholder="Any additional information about your needs"
                    ></textarea>
                  </div>

                  <button 
                    type="submit"
                    className="w-full bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-6 rounded-md transition-colors duration-200"
                  >
                    Submit Intake Form
                  </button>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}