import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Users,
    Briefcase,
    Clock,
    CheckCircle,
    Building2,
    Headphones,
    Laptop,
    BarChart,
    Zap,
    Shield,
    HeartHandshake,
    Award,
    ArrowRight,
    UserCheck,
    Layers,
    Lightbulb
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface BenefitItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

interface ReasonItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

const benefits: BenefitItem[] = [
    {
        icon: Briefcase,
        title: 'Dedicated Resources',
        description: 'A team that works exclusively for your business, becoming experts in your processes and systems.'
    },
    {
        icon: Clock,
        title: 'Time Zone Alignment',
        description: 'Our teams work in your preferred time zone, ensuring real-time collaboration when you need it most.'
    },
    {
        icon: Headphones,
        title: 'Responsive Support',
        description: 'Direct communication channels with your team lead for quick issue resolution and updates.'
    },
    {
        icon: Laptop,
        title: 'Technical Expertise',
        description: 'Skilled professionals trained in industry-standard tools, software, and best practices.'
    }
];

const reasons: ReasonItem[] = [
    {
        icon: BarChart,
        title: 'Increased Productivity',
        description: 'Dedicated employees focus solely on your business, leading to higher output and efficiency.'
    },
    {
        icon: Zap,
        title: 'Faster Turnaround',
        description: 'With a team that knows your business inside out, tasks are completed more quickly and accurately.'
    },
    {
        icon: Shield,
        title: 'Enhanced Security',
        description: 'Dedicated teams provide better data security than rotating staff or freelancers.'
    },
    {
        icon: HeartHandshake,
        title: 'Stronger Relationships',
        description: 'Build meaningful working relationships with team members who understand your business goals.'
    }
];

export function TeamPage() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1600880292089-90a7e086ee0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <Users className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Our Team
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">Meet Your Dedicated Support Team</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Skilled professionals committed to your business success
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                                    <Award className="h-5 w-5 text-[#b19763] mr-2" />
                                    <span className="text-white font-medium">Experienced • Dedicated • Reliable</span>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    
                    {/* Dedicated Employee Model Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <UserCheck className="h-7 w-7 text-[#b19763]" />
                            The Dedicated Employee Model
                        </motion.h3>

                        <div className="max-w-4xl mx-auto">
                            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                                At Backsure Global Support, we believe in the power of dedicated teams. Unlike traditional outsourcing where your tasks might be handled by different people each time, our dedicated employee model assigns specific professionals to work exclusively with your business. This creates continuity, deeper understanding of your processes, and ultimately better results.
                            </p>
                            
                            <div className="grid md:grid-cols-2 gap-8 mb-8">
                                <motion.div 
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.4, duration: 0.5 }}
                                    className="bg-gray-50 p-6 rounded-xl border border-gray-100">
                                    <h4 className="text-lg font-semibold text-[#062767] mb-3 flex items-center gap-2">
                                        <Building2 className="h-5 w-5 text-[#b19763]" />
                                        Our Team Structure
                                    </h4>
                                    <p className="text-gray-600">
                                        Each client is assigned a dedicated team led by an experienced Team Lead who serves as your primary point of contact. Your team members are selected based on the specific skills your business needs, ensuring the perfect fit for your operations.
                                    </p>
                                </motion.div>
                                
                                <motion.div 
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.5, duration: 0.5 }}
                                    className="bg-gray-50 p-6 rounded-xl border border-gray-100">
                                    <h4 className="text-lg font-semibold text-[#062767] mb-3 flex items-center gap-2">
                                        <Layers className="h-5 w-5 text-[#b19763]" />
                                        Our Approach
                                    </h4>
                                    <p className="text-gray-600">
                                        We take time to understand your business processes, systems, and goals before assigning your team. This thorough onboarding ensures your dedicated employees can hit the ground running and deliver value from day one.
                                    </p>
                                </motion.div>
                            </div>
                        </div>
                    </div>

                    {/* What You Get Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <CheckCircle className="h-7 w-7 text-[#b19763]" />
                            What You Get
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {benefits.map((item, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <item.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{item.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Why It Works Section */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.5 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <Lightbulb className="h-7 w-7 text-[#b19763]" />
                            Why It Works
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {reasons.map((item, index) => (
                                <motion.div 
                                    key={index}
                                    variants={fadeIn} 
                                    className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                    <div className="flex items-center gap-3 mb-3">
                                        <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                            <item.icon className="h-5 w-5 text-[#b19763]" />
                                        </div>
                                        <h4 className="text-lg font-semibold text-[#062767]">{item.title}</h4>
                                    </div>
                                    <p className="text-gray-600 group-hover:text-gray-700">
                                        {item.description}
                                    </p>
                                </motion.div>
                            ))}
                        </motion.div>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Ready to Build Your Dedicated Team?
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                Let's discuss how our dedicated employee model can help your business grow while reducing operational costs.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <Users className="h-5 w-5" />
                                Schedule a Consultation
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}