import { <PERSON><PERSON><PERSON>, FileText, Calculator, BarChart2, DollarSign, CheckCircle, TrendingUp, ArrowRight } from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export function FinancePage() {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Hero Section with Parallax Effect */}
                <div className="relative overflow-hidden rounded-2xl mb-16">
                    <div className="absolute inset-0 bg-cover bg-center" 
                         style={{ backgroundImage: 'url(https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1911&q=80)', 
                                filter: 'brightness(0.4)' }}>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-[#062767]/90 to-[#062767]/70"></div>
                    <motion.div 
                        initial="hidden"
                        animate={isVisible ? "visible" : "hidden"}
                        variants={fadeIn}
                        transition={{ duration: 0.6 }}
                        className="relative py-20 px-6 text-center">
                        <div className="max-w-3xl mx-auto">
                            <motion.div 
                                initial={{ scale: 0.8, opacity: 0 }}
                                animate={{ scale: 1, opacity: 1 }}
                                transition={{ duration: 0.5 }}
                                className="inline-block p-2 px-4 rounded-full bg-white/10 backdrop-blur-sm mb-4">
                                <span className="text-white/90 text-sm font-medium flex items-center">
                                    <DollarSign className="h-4 w-4 mr-2 text-[#b19763]" />
                                    Financial Excellence
                                </span>
                            </motion.div>
                            <h2 className="text-5xl font-bold text-white mb-6">Finance & Accounting Support</h2>
                            <p className="text-xl text-white/90 mb-8">
                                Designed to Streamline Your Financial Operations and Drive Smarter Decisions
                            </p>
                            <motion.div 
                                initial={{ y: 20, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="flex justify-center">
                                <div className="p-3 bg-white/10 backdrop-blur-sm rounded-lg inline-flex items-center">
                                    <TrendingUp className="h-5 w-5 text-[#b19763] mr-2" />
                                    <span className="text-white font-medium">Reliable • Accurate • Scalable</span>
                                </div>
                            </motion.div>
                        </div>
                    </motion.div>
                </div>

                {/* Main Content with Card Animation */}
                <motion.div 
                    initial="hidden"
                    animate={isVisible ? "visible" : "hidden"}
                    variants={fadeIn}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="bg-white rounded-2xl shadow-xl p-8 mb-12 border border-gray-100">
                    <div className="max-w-4xl mx-auto">
                        <p className="text-lg text-gray-700 mb-8 leading-relaxed">
                            Reliable, accurate, and scalable solutions tailored for growing businesses. We ensure compliance, cash-flow positivity, and strategic financial clarity.
                            At BackSure Global Support, we take care of the numbers - whether you're a startup, SME, or enterprise. Our expert team streamlines your financial workflows so you can focus on growth, strategy, and execution.
                        </p>
                    </div>

                    {/* Core Services Section with Hover Effects */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.3 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <DollarSign className="h-7 w-7 text-[#b19763]" />
                            Our Core Services
                        </motion.h3>

                        <motion.div 
                            variants={staggerContainer}
                            initial="hidden"
                            animate="visible"
                            className="grid md:grid-cols-2 gap-6">
                            {/* Service 1 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <BookOpen className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Bookkeeping</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Maintain clean, audit-ready books with daily/weekly/monthly services using QuickBooks, Xero, or Zoho Books.
                                </p>
                            </motion.div>

                            {/* Service 2 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <FileText className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">AP/AR Management</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Optimize cash flow with timely vendor payments and consistent receivables follow-ups.
                                </p>
                            </motion.div>

                            {/* Service 3 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <Calculator className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">VAT & Corporate Tax</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    UAE/GCC focused compliance for VAT registration, filings, and Corporate Tax preparation.
                                </p>
                            </motion.div>

                            {/* Service 4 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <BarChart2 className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Financial Reporting</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Monthly/quarterly P&L, balance sheets, cash flow summaries, and audit coordination.
                                </p>
                            </motion.div>

                            {/* Service 5 */}
                            <motion.div variants={fadeIn} className="group bg-gray-50 hover:bg-[#f8f5ee] p-6 rounded-xl border border-transparent hover:border-[#b19763]/20 transition-all duration-300 md:col-span-2">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="p-2 bg-white rounded-lg shadow-sm group-hover:shadow group-hover:bg-[#b19763]/10 transition-all duration-300">
                                        <TrendingUp className="h-5 w-5 text-[#b19763]" />
                                    </div>
                                    <h4 className="text-lg font-semibold text-[#062767]">Budgeting</h4>
                                </div>
                                <p className="text-gray-600 group-hover:text-gray-700">
                                    Data-driven budget preparation, variance analysis, and rolling forecasts.
                                </p>
                            </motion.div>
                        </motion.div>
                    </div>

                    {/* Why It Matters Section with Animated Icons */}
                    <div className="mb-12">
                        <motion.h3 
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: 0.4 }}
                            className="text-2xl font-bold text-[#062767] mb-8 flex items-center gap-2 border-b pb-4 border-gray-100">
                            <CheckCircle className="h-7 w-7 text-[#b19763]" />
                            Why It Matters
                        </motion.h3>

                        <div className="grid md:grid-cols-3 gap-8">
                            {/* Benefit 1 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.5, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <CheckCircle className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Accuracy & Transparency</h4>
                                <p className="text-gray-600">
                                    Error-free financials with real-time data access for informed decision-making.
                                </p>
                            </motion.div>

                            {/* Benefit 2 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.6, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <FileText className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Regulatory Compliance</h4>
                                <p className="text-gray-600">
                                    Stay aligned with UAE/GCC tax laws without last-minute surprises.
                                </p>
                            </motion.div>

                            {/* Benefit 3 */}
                            <motion.div 
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.7, duration: 0.5 }}
                                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-[#b19763]/20 text-center"
                            >
                                <div className="mx-auto mb-4 p-4 bg-[#f8f5ee] rounded-full w-20 h-20 flex items-center justify-center">
                                    <TrendingUp className="h-10 w-10 text-[#b19763]" />
                                </div>
                                <h4 className="text-xl font-semibold text-[#062767] mb-3">Scalable Support</h4>
                                <p className="text-gray-600">
                                    Services that grow with you from startup to enterprise.
                                </p>
                            </motion.div>
                        </div>
                    </div>

                    {/* Closing CTA with Gradient and Animation */}
                    <motion.div 
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7, duration: 0.5 }}
                        className="relative overflow-hidden rounded-2xl">
                        <div className="absolute inset-0 bg-gradient-to-r from-[#062767] to-[#0a3a8a]"></div>
                        <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1715&q=80')] opacity-10 bg-cover bg-center mix-blend-overlay"></div>
                        <div className="relative p-10 text-center">
                            <h3 className="text-3xl font-bold text-white mb-4">
                                Numbers Made Simple. Finances Done Right.
                            </h3>
                            <p className="text-white/90 mb-8 max-w-3xl mx-auto text-lg">
                                Let us manage your finance and accounting while you focus on business growth with confidence.
                            </p>
                            <button 
                                className="bg-white hover:bg-[#f8f5ee] text-[#062767] font-medium py-3 px-8 rounded-lg transition-colors duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl"
                                onClick={() => window.location.href = "/pg_layouts/contact"}
                            >
                                <FileText className="h-5 w-5" />
                                Request a Consultation
                                <ArrowRight className="h-5 w-5 ml-1 group-hover:translate-x-1 transition-transform" />
                            </button>
                        </div>
                    </motion.div>
                </motion.div>
            </div>
        </section>
    );
}