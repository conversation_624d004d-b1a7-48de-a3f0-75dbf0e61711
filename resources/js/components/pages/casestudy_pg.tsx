import { BookOpen, Mail, Calendar, ArrowRight, ChevronLeft, ChevronRight, Building, BarChart, Users, Clock, Sparkles, ArrowUpRight } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

export function CaseStudyPage() {
    // Animation refs for scroll reveal
    const headerRef = useRef<HTMLDivElement>(null);
    const titleRef = useRef<HTMLHeadingElement>(null);
    const cardsRef = useRef<HTMLDivElement>(null);
    
    // Track if elements are in view
    const [headerInView, setHeaderInView] = useState(false);
    const [titleInView, setTitleInView] = useState(false);
    const [cardsInView, setCardsInView] = useState(false);
    
    // Set up intersection observer for animations
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.target === headerRef.current) {
                        setHeaderInView(entry.isIntersecting);
                    } else if (entry.target === titleRef.current) {
                        setTitleInView(entry.isIntersecting);
                    } else if (entry.target === cardsRef.current) {
                        setCardsInView(entry.isIntersecting);
                    }
                });
            },
            { threshold: 0.1 }
        );

        if (headerRef.current) observer.observe(headerRef.current);
        if (titleRef.current) observer.observe(titleRef.current);
        if (cardsRef.current) observer.observe(cardsRef.current);

        return () => {
            if (headerRef.current) observer.unobserve(headerRef.current);
            if (titleRef.current) observer.unobserve(titleRef.current);
            if (cardsRef.current) observer.unobserve(cardsRef.current);
        };
    }, []);

    // Sample case studies data
    const caseStudies = [
        {
            title: "Streamlining Insurance Operations for Regional Broker",
            industry: "Insurance",
            date: "March 2025",
            challenge: "A growing insurance broker faced operational bottlenecks with policy processing and client service as they expanded across the region.",
            solution: "Implemented dedicated team of 5 specialists handling policy administration, renewals, and client service, with custom workflows aligned to broker's systems.",
            results: "50% reduction in policy processing time, 30% increase in renewal rates, and improved client satisfaction scores.",
            icon: Building,
            color: "bg-blue-100 text-blue-700",
            image: "https://source.unsplash.com/random/?insurance,business,office"
        },
        {
            title: "Financial Reporting Transformation for Tech Startup",
            industry: "Finance & Technology",
            date: "February 2025",
            challenge: "Fast-growing SaaS company struggled with financial visibility and reporting accuracy during rapid expansion phase.",
            solution: "Deployed finance specialists to implement standardized reporting processes, cash flow management, and investor-ready financial statements.",
            results: "Reduced month-end closing from 15 to 3 days, secured additional funding based on improved financial transparency.",
            icon: BarChart,
            color: "bg-amber-100 text-amber-700",
            image: "https://source.unsplash.com/random/?finance,technology,charts"
        },
        {
            title: "HR Process Optimization for Multinational Firm",
            industry: "Human Resources",
            date: "January 2025",
            challenge: "Company with 200+ employees across 5 countries struggled with inconsistent HR processes and compliance issues.",
            solution: "Established centralized HR support team handling payroll, onboarding, benefits administration and compliance documentation.",
            results: "95% reduction in compliance-related issues, standardized employee experience, and 40% time savings for internal HR leadership.",
            icon: Users,
            color: "bg-green-100 text-green-700",
            image: "https://source.unsplash.com/random/?humanresources,team,office"
        },
        {
            title: "Business Administration Support for Seasonal Business",
            industry: "Retail & E-commerce",
            date: "December 2024",
            challenge: "Seasonal business faced staffing challenges during peak periods, leading to administrative backlogs and customer service issues.",
            solution: "Provided flexible on-demand support team that scaled from 2 to 12 specialists during peak seasons, handling customer service, order processing, and inventory management.",
            results: "Maintained 24-hour response times during 300% volume increase, eliminated overtime costs, and improved customer retention.",
            icon: Clock,
            color: "bg-purple-100 text-purple-700",
            image: "https://source.unsplash.com/random/?retail,ecommerce,business"
        }
    ];

    return (
        <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Enhanced Page Header with Animation */}
                <div 
                    ref={headerRef}
                    className={cn(
                        "relative overflow-hidden bg-gradient-to-r from-[#062767] to-[#0a3a8a] py-20 px-6 mb-16 rounded-2xl shadow-lg transition-all duration-700 transform",
                        headerInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
                    )}
                >
                    {/* Decorative elements */}
                    <div className="absolute top-0 right-0 w-64 h-64 bg-[#b19763] opacity-10 rounded-full -translate-y-1/2 translate-x-1/2 blur-3xl animate-blob"></div>
                    <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-400 opacity-10 rounded-full translate-y-1/2 -translate-x-1/2 blur-3xl animate-blob animation-delay-2000"></div>
                    
                    {/* Floating accent elements */}
                    <div className="absolute top-12 right-12 bg-white/10 backdrop-blur-md p-3 rounded-full animate-float">
                        <Sparkles className="h-6 w-6 text-[#b19763]" />
                    </div>
                    <div className="absolute bottom-12 left-12 bg-white/10 backdrop-blur-md p-3 rounded-full animate-float animation-delay-1000">
                        <BookOpen className="h-6 w-6 text-[#b19763]" />
                    </div>
                    
                    <div className="max-w-3xl mx-auto text-center relative z-10">
                        <h2 className="text-5xl font-bold text-white mb-6 leading-tight">
                            <span className="relative inline-block">
                                Case Studies
                                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#b19763] to-[#d9c08c] rounded-full"></span>
                            </span>
                        </h2>
                        <p className="text-xl text-white/90 leading-relaxed">
                            Real results from real clients. See how we've helped businesses like yours overcome challenges and achieve their goals.
                        </p>
                    </div>
                </div>

                {/* Case Studies Section */}
                <div className="mb-16">
                    <h3 
                        ref={titleRef}
                        className={cn(
                            "text-2xl font-bold text-[#062767] mb-8 flex items-center gap-3 transition-all duration-700 transform",
                            titleInView ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-10"
                        )}
                    >
                        <div className="p-2 rounded-lg bg-gradient-to-br from-[#b19763] to-[#d9c08c] text-white">
                            <BookOpen className="h-6 w-6" />
                        </div>
                        <span className="bg-gradient-to-r from-[#062767] to-[#0a3a8a] bg-clip-text text-transparent">
                            Success Stories
                        </span>
                    </h3>

                    <div 
                        ref={cardsRef}
                        className={cn(
                            "grid md:grid-cols-2 gap-8 transition-all duration-1000",
                            cardsInView ? "opacity-100 translate-y-0" : "opacity-0 translate-y-20"
                        )}
                    >
                        {caseStudies.map((study, index) => {
                            const Icon = study.icon;
                            return (
                                <Card 
                                    key={index} 
                                    className={cn(
                                        "bg-white hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden group",
                                        "transform hover:-translate-y-1"
                                    )}
                                    style={{
                                        transitionDelay: `${index * 100}ms`
                                    }}
                                >
                                    {/* Add image section */}
                                    <div className="relative h-48 w-full overflow-hidden">
                                        <img 
                                            src={study.image} 
                                            alt={study.title}
                                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                                        />
                                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-70"></div>
                                        <div className="absolute bottom-4 left-4 right-4">
                                            <CardTitle className="text-xl font-bold text-white group-hover:text-white/90 transition-colors duration-300">
                                                {study.title}
                                            </CardTitle>
                                        </div>
                                    </div>
                                    
                                    <CardHeader>
                                        <div className="flex items-center justify-between">
                                            <div className={cn("flex items-center gap-2 text-sm font-medium px-3 py-1 rounded-full", study.color)}>
                                                <Icon className="h-4 w-4" />
                                                {study.industry}
                                            </div>
                                            <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-1 rounded-full">
                                                <Calendar className="h-4 w-4 text-[#062767]" />
                                                <span className="text-gray-700 font-medium">{study.date}</span>
                                            </div>
                                        </div>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:bg-gray-100">
                                            <h4 className="font-semibold text-gray-900 mb-1 flex items-center gap-2">
                                                <span className="w-2 h-2 rounded-full bg-red-500"></span>
                                                Challenge:
                                            </h4>
                                            <p className="text-gray-700">{study.challenge}</p>
                                        </div>
                                        <div className="bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:bg-gray-100">
                                            <h4 className="font-semibold text-gray-900 mb-1 flex items-center gap-2">
                                                <span className="w-2 h-2 rounded-full bg-amber-500"></span>
                                                Solution:
                                            </h4>
                                            <p className="text-gray-700">{study.solution}</p>
                                        </div>
                                        <div className="bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:bg-gray-100">
                                            <h4 className="font-semibold text-gray-900 mb-1 flex items-center gap-2">
                                                <span className="w-2 h-2 rounded-full bg-green-500"></span>
                                                Results:
                                            </h4>
                                            <p className="text-gray-700">{study.results}</p>
                                        </div>
                                    </CardContent>
                                    <CardFooter>
                                        <button className="text-[#b19763] hover:text-[#062767] font-medium flex items-center gap-2 transition-colors duration-200 group-hover:translate-x-1 transform transition-transform">
                                            Read Full Case Study
                                            <ArrowUpRight className="h-4 w-4 transition-transform group-hover:scale-125" />
                                        </button>
                                    </CardFooter>
                                </Card>
                            );
                        })}
                    </div>
                </div>

                {/* Enhanced Pagination */}
                <div className="flex justify-center items-center gap-2 mb-16">
                    <button className="p-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-all duration-200 text-[#062767] hover:border-[#062767]">
                        <ChevronLeft className="h-5 w-5" />
                    </button>
                    
                    <button className="w-10 h-10 rounded-md bg-gradient-to-r from-[#062767] to-[#0a3a8a] text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 transform">
                        1
                    </button>
                    
                    <button className="w-10 h-10 rounded-md border border-gray-300 hover:bg-gray-100 transition-all duration-200 font-medium text-[#062767] hover:border-[#062767] hover:-translate-y-0.5 transform">
                        2
                    </button>
                    
                    <button className="p-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-all duration-200 text-[#062767] hover:border-[#062767]">
                        <ChevronRight className="h-5 w-5" />
                    </button>
                </div>
                
                {/* Enhanced Closing CTA */}
                <div className="relative overflow-hidden bg-gradient-to-br from-[#f8f5ee] to-white p-10 rounded-2xl shadow-lg border border-[#b19763]/20">
                    {/* Decorative elements */}
                    <div className="absolute -top-20 -right-20 w-64 h-64 bg-[#b19763] opacity-10 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
                    
                    <div className="relative z-10 text-center">
                        <h3 className="text-3xl font-bold bg-gradient-to-r from-[#062767] to-[#0a3a8a] bg-clip-text text-transparent mb-6">
                            Ready to Become Our Next Success Story?
                        </h3>
                        <p className="text-black/80 mb-8 max-w-3xl mx-auto text-lg">
                            Let us help you overcome your business challenges and achieve measurable results with our tailored support solutions.
                        </p>
                        <button 
                            className="bg-gradient-to-r from-[#062767] to-[#0a3a8a] hover:from-[#0a3a8a] hover:to-[#062767] text-white font-medium py-4 px-8 rounded-xl transition-all duration-300 inline-flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                            onClick={() => window.location.href = "/pg_layouts/contact"}
                        >
                            <Mail className="h-5 w-5" />
                            Request a Consultation
                        </button>
                    </div>
                </div>
            </div>
        </section>
    );
}