import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { type SharedData } from '@/types';
import { ArrowRight } from 'lucide-react';

export function FinanceHero() {
    const { auth } = usePage<SharedData>().props;

    return (
        <section className="pt-24 pb-16 bg-gradient-to-br from-blue-50 via-white to-blue-50 overflow-hidden">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1">
                    {/* Left Content - Full width since right side is removed */}
                    <div className="text-center lg:text-left">

                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                            Finance & Accounting <span className="text-blue-600">Support</span>
                        </h1>
                        <p className="text-xl text-gray-700 mb-4 font-medium">
                            Designed to <span className="font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Streamline Your Financial Operations</span>
                        </p>
                        <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                            Reliable, accurate, and scalable solutions tailored for growing businesses. We ensure compliance, cash-flow positivity, and strategic financial clarity.
                        </p>
                        <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                            At BackSure Global Support, we take care of the numbers - whether you're a startup, SME, or enterprise. Our expert team streamlines your financial workflows so you can focus on growth, strategy, and execution.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4  justify-center lg:text-center items-center">
                            <Button 
                                size="lg" 
                                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all" 
                                asChild
                            >
                                <Link href="/contact">
                                    Get Financial Consultation
                                    <ArrowRight className="ml-2 h-5 w-5" />
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}