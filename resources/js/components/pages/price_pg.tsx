import { BookOpen, Mail, Calendar, ArrowRight, ChevronLeft, ChevronRight, CheckCircle, Shield, Users, DollarSign, Clock, Zap, Star, Info, FileText } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';

interface PricingPlan {
    name: string;
    description: string;
    price: string;
    priceDetail: string;
    features: string[];
    buttonText: string;
    buttonHref: string;
    popular?: boolean;
    icon: React.ComponentType<{ className?: string }>;
    gradient: string;
}

const pricingPlans: PricingPlan[] = [
    {
        name: 'Small Team Bundle',
        description: 'Perfect for startups and small teams',
        price: 'Custom',
        priceDetail: 'Based on your needs',
        features: [
            'Up to 3 dedicated team members',
            'Basic administrative support',
            'Monthly reporting',
            'Email support',
            'Business hours availability'
        ],
        buttonText: 'Get Started',
        buttonHref: '/pg_layouts/contact',
        icon: Users,
        gradient: 'from-primary to-primary'
    },
    {
        name: 'Growth Bundle',
        description: 'Ideal for growing businesses',
        price: 'Custom',
        priceDetail: 'Based on your needs',
        features: [
            'Up to 5 dedicated team members',
            'Full administrative support',
            'Weekly reporting',
            'Priority email & phone support',
            'Extended hours availability',
            'Compliance monitoring'
        ],
        buttonText: 'Get Started',
        buttonHref: '/pg_layouts/contact',
        popular: true,
        icon: Zap,
        gradient: 'from-secondary to-secondary'
    },
    {
        name: 'Enterprise Bundle',
        description: 'For established organizations',
        price: 'Custom',
        priceDetail: 'Based on your needs',
        features: [
            'Unlimited team members',
            'Complete back-office solution',
            'Real-time reporting dashboard',
            'Dedicated account manager',
            '24/7 availability',
            'Full compliance management',
            'Strategic business consulting'
        ],
        buttonText: 'Get Started',
        buttonHref: '/pg_layouts/contact',
        icon: Shield,
        gradient: 'from-primary to-primary'
    }
];

export function PricePage() {
    const [isVisible, setIsVisible] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.pricing-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);

    return (
        <section className="py-16 bg-gray-50 pricing-section relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
                <div className="absolute top-20 right-20 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-secondary/5 rounded-full blur-3xl"></div>
            </div>
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                {/* Page Header */}
                <div className="relative py-20 px-4 mb-16 rounded-lg overflow-hidden">
                    <div className="absolute inset-0 z-0">
                        <img 
                            src="https://source.unsplash.com/random/?finance,accounting,business" 
                            alt="Finance and Accounting Services" 
                            className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-[#b19763]/90"></div>
                    </div>
                    <div className="max-w-3xl mx-auto text-center relative z-10">
                        <h2 className="text-4xl font-bold text-white mb-4">Professional Business Support Solutions</h2>
                        <p className="text-xl text-white opacity-90">
                            Choose from our comprehensive range of professional services designed to support your business operations. From dedicated teams to specialized industry solutions for insurance, finance, and enterprise operations.
                        </p>
                    </div>
                </div>
                

                {/* Professional Services Section */}
                <div className="mb-20">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Professional Services</h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">Comprehensive business support solutions tailored to your needs</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Service 1 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary rounded-lg flex items-center justify-center mb-4">
                                    <Users className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">Dedicated Team</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Full-time professionals working exclusively on your projects with commitment and focus.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                        
                        {/* Service 2 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-secondary to-secondary rounded-lg flex items-center justify-center mb-4">
                                    <Zap className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">On-Demand Support</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Flexible support services available when you need them, scaling with your requirements.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                        
                        {/* Service 3 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary rounded-lg flex items-center justify-center mb-4">
                                    <Shield className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">Business Care Plans</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Comprehensive packages to maintain and optimize your business operations.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                        
                        {/* Service 4 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-secondary to-secondary rounded-lg flex items-center justify-center mb-4">
                                    <FileText className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">Insurance Operations</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Specialized support for brokers, aggregators, and TPAs across insurance segments.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                        
                        {/* Service 5 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary rounded-lg flex items-center justify-center mb-4">
                                    <DollarSign className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">Finance & Accounting</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Reliable financial services from bookkeeping to reporting and compliance.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                        
                        {/* Service 6 */}
                        <Card className="border border-gray-200 hover:border-primary/20 hover:shadow-lg transition-all duration-300">
                            <CardHeader>
                                <div className="w-12 h-12 bg-gradient-to-br from-secondary to-secondary rounded-lg flex items-center justify-center mb-4">
                                    <FileText className="w-6 h-6 text-white" />
                                </div>
                                <CardTitle className="text-xl font-bold text-gray-900">Compliance & Administration</CardTitle>
                                <CardDescription className="text-gray-600">
                                    Stay legally compliant with expert administrative and regulatory support.
                                </CardDescription>
                            </CardHeader>
                        </Card>
                    </div>
                </div>
                
                {/* Service Packages Section */}
                <div className="mb-20">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">Service Packages</h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">Save up to 25% when you combine multiple professional services into a custom package tailored to your business needs.</p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        {pricingPlans.map((plan, index) => {
                            const Icon = plan.icon;
                            return (
                                <div key={plan.name} className="relative">
                                    {plan.popular && (
                                        <div className="absolute -top-4 inset-x-0 flex justify-center">
                                            <Badge variant="secondary" className="px-3 py-1 text-xs font-medium">
                                                Most Popular
                                            </Badge>
                                        </div>
                                    )}
                                    <Card 
                                        className={`h-full border-gray-200 transition-all duration-500 hover:shadow-xl hover:border-primary/20 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'} ${plan.popular ? 'border-secondary/30 shadow-md' : ''}`}
                                        style={{ transitionDelay: `${index * 200}ms` }}
                                    >
                                        <CardHeader>
                                            <div 
                                                className={`w-16 h-16 bg-gradient-to-br ${plan.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 relative`}
                                                style={{ boxShadow: `0 10px 15px -3px ${plan.gradient.includes('primary') ? 'rgba(10, 58, 138, 0.2)' : 'rgba(177, 151, 99, 0.2)'}` }}
                                            >
                                                <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                                <Icon className="w-8 h-8 text-white" />
                                            </div>
                                            <CardTitle className="text-2xl font-bold text-gray-900">{plan.name}</CardTitle>
                                            <CardDescription className="text-gray-600 mt-2">{plan.description}</CardDescription>
                                            <div className="mt-6 mb-2">
                                                <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                                                <span className="text-gray-500 ml-2">{plan.priceDetail}</span>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <ul className="space-y-3">
                                                {plan.features.map((feature, featureIndex) => (
                                                    <li key={featureIndex} className="flex items-center text-gray-700">
                                                        <CheckCircle className="flex-shrink-0 mr-3 w-5 h-5 text-secondary" />
                                                        {feature}
                                                    </li>
                                                ))}
                                            </ul>
                                        </CardContent>
                                        <CardFooter>
                                            <Button 
                                                className="w-full bg-[#062767] hover:bg-[#0a3a8a] text-white"
                                                onClick={() => window.location.href = plan.buttonHref}
                                            >
                                                {plan.buttonText}
                                            </Button>
                                        </CardFooter>
                                    </Card>
                                </div>
                            );
                        })}
                    </div>
                </div>
                
                {/* Custom Solutions Section */}
                <div className="bg-white rounded-lg shadow-md p-8 mb-12">
                    <div className="text-center mb-8">
                        <h2 className="text-3xl font-bold text-gray-900 mb-4">Custom Solutions</h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            Need something unique? Our team will work with you to design a tailored business support solution that fits your exact requirements and industry needs. Contact us for a consultation.
                        </p>
                    </div>
                    <div className="flex justify-center">
                        <Button 
                            className="bg-[#062767] hover:bg-[#0a3a8a] text-white font-medium py-3 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2"
                            onClick={() => window.location.href = "/pg_layouts/contact"}
                        >
                            <Mail className="h-5 w-5" />
                            Request a Quote
                        </Button>
                    </div>
                </div>
                
                {/* Consultation Section */}
                <div className="relative p-12 rounded-lg text-center overflow-hidden mb-12">
                    <div className="absolute inset-0 z-0">
                        <img 
                            src="https://source.unsplash.com/random/?business,professional,office" 
                            alt="Professional Business Support" 
                            className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-primary/90"></div>
                    </div>
                    <div className="relative z-10">
                        <h3 className="text-3xl font-bold text-white mb-4">
                            Free Consultation: Discover the right solution for your business
                        </h3>
                        <p className="text-white mb-6 max-w-3xl mx-auto opacity-90">
                            Expert Assessment: Get a personalized evaluation of your business needs.
                            Flexible Engagement: Choose from various service models with no long-term commitment required.
                        </p>
                        <Button 
                            className="bg-white hover:bg-gray-100 text-[#062767] font-medium py-3 px-8 rounded-md transition-colors duration-200 inline-flex items-center gap-2"
                            onClick={() => window.location.href = "/pg_layouts/contact"}
                        >
                            <Calendar className="h-5 w-5" />
                            Schedule a Consultation
                        </Button>
                    </div>
                </div>
            </div>
        </section>
    );
}