import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { <PERSON>R<PERSON>, CheckCircle, <PERSON>rkles, MessageSquare, ArrowUpRight } from 'lucide-react';
import { useEffect, useState } from 'react';

interface CtaSectionProps {
    title?: string;
    description?: string;
    buttonText?: string;
    buttonHref?: string;
    className?: string;
}

export function CtaSection({
    title = "Ready to Transform Your Business Support?",
    description = "Let's create a tailored solution that drives growth and reduces operational costs.",
    buttonText = "Schedule a Consultation",
    buttonHref = "/contact",
    className = ""
}: CtaSectionProps) {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );

        const section = document.querySelector('.cta-section');
        if (section) observer.observe(section);

        return () => observer.disconnect();
    }, []);

    return (
        <section className="overflow-hidden relative py-20 cta-section">
            {/* Background with gradient and pattern */}
            <div className="absolute inset-0 bg-gradient-to-br via-white from-secondary/10 to-primary/10"></div>
            
            {/* Animated background elements */}
            <div className="overflow-hidden absolute inset-0 pointer-events-none">
                <div className="absolute top-0 left-1/4 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-blob"></div>
                <div className="absolute bottom-0 right-1/4 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-blob animation-delay-2000"></div>
                <div className="absolute top-1/2 left-1/2 w-96 h-96 rounded-full blur-3xl transform -translate-x-1/2 -translate-y-1/2 bg-primary/5 animate-blob animation-delay-4000"></div>
            </div>

            <div className="relative z-10 px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 gap-12 items-center lg:grid-cols-2">
                    {/* Left Content */}
                    <div className={`transition-all duration-1000 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
                        <div className="inline-flex items-center px-3 py-1 mb-6 text-sm font-medium rounded-full bg-secondary/10 text-secondary">
                            <Sparkles className="mr-2 w-4 h-4" />
                            <span>Elevate Your Business</span>
                        </div>
                        
                        <h2 className="mb-6 text-3xl font-bold text-gray-900 md:text-4xl lg:text-5xl">
                            {title}
                        </h2>
                        
                        <p className="mb-8 text-xl text-gray-700">
                            {description}
                        </p>
                        
                        <div className="mb-8 space-y-4">
                            <div className="flex items-center space-x-3">
                                <div className="flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-full bg-primary/10">
                                    <CheckCircle className="w-5 h-5 text-primary" />
                                </div>
                                <span className="text-gray-800">Dedicated support team tailored to your needs</span>
                            </div>
                            
                            <div className="flex items-center space-x-3">
                                <div className="flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-full bg-primary/10">
                                    <CheckCircle className="w-5 h-5 text-primary" />
                                </div>
                                <span className="text-gray-800">Flexible contracts with transparent pricing</span>
                            </div>
                            
                            <div className="flex items-center space-x-3">
                                <div className="flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-full bg-primary/10">
                                    <CheckCircle className="w-5 h-5 text-primary" />
                                </div>
                                <span className="text-gray-800">Proven results with measurable ROI</span>
                            </div>
                        </div>
                    </div>
                    
                    {/* Right Card */}
                    <div className={`transition-all duration-1000 delay-300 transform ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
                        <div className="overflow-hidden relative p-8 bg-white rounded-2xl border border-gray-100 shadow-xl transition-shadow duration-300 hover:shadow-2xl hover:shadow-primary/10">
                            {/* Card decorative elements */}
                            <div className="absolute top-0 right-0 w-32 h-32 rounded-full translate-x-1/2 -translate-y-1/2 bg-secondary/5"></div>
                            <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full -translate-x-1/2 translate-y-1/2 bg-primary/5"></div>
                            
                            <div className="relative">
                                <div className="flex items-center mb-6 space-x-3">
                                    <div className="flex flex-shrink-0 justify-center items-center w-10 h-10 rounded-lg bg-primary">
                                        <MessageSquare className="w-5 h-5 text-white" />
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900">Get Started Today</h3>
                                </div>
                                
                                <p className="mb-6 text-gray-700">
                                    Our team is ready to help you design a custom support model that aligns with your business goals and budget.
                                </p>
                                
                                <div className="space-y-4">
                                    <Button 
                                        size="lg" 
                                        className="w-full text-white shadow-lg transition-all bg-primary hover:bg-primary/90 hover:shadow-xl group"
                                        asChild
                                    >
                                        <a href={buttonHref}>
                                            {buttonText}
                                            <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
                                        </a>
                                    </Button>
                                    
                                    <Button 
                                        variant="outline" 
                                        className="w-full text-gray-700 border-gray-200 hover:bg-gray-50 group"
                                        asChild
                                    >
                                        <a href="/services">
                                            Explore Our Services
                                            <ArrowUpRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
                                        </a>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
