import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Target,
    Settings,
    Users,
    Globe,
    Star
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface Feature {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
}

const features: Feature[] = [
    {
        icon: Target,
        title: 'Core Service Domains',
        description: 'Supporting businesses across industries'
    },
    {
        icon: Settings,
        title: 'Flexible Contract',
        description: 'Simple terms with transparent monthly billing'
    },
    {
        icon: Users,
        title: 'Tailored Support',
        description: 'Designed to fit startups, SMEs, and large enterprises'
    },
    {
        icon: Globe,
        title: 'Global Support',
        description: 'Always available, wherever you are'
    },
    {
        icon: Star,
        title: 'Positive Client Experiences',
        description: 'Trusted by early clients and growing partnerships'
    }
];

export function FeaturesSection() {
    const [isVisible, setIsVisible] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.features-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);
    
    return (
        <section className="py-16 bg-white features-section relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-50">
                <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-secondary/5 rounded-full blur-3xl"></div>
            </div>
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Features</h2>
                    <p className="text-xl text-gray-600">Amazing features we offer</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {features.map((feature, index) => (
                        <FeatureCard 
                            key={feature.title} 
                            feature={feature} 
                            index={index} 
                            isVisible={isVisible}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface FeatureCardProps {
    feature: Feature;
    index: number;
    isVisible: boolean;
}

function FeatureCard({ feature, index, isVisible }: FeatureCardProps) {
    const Icon = feature.icon;
    
    return (
        <Card 
            className={`group hover:shadow-lg transition-all duration-300 border-gray-200 hover:border-primary/20 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
            style={{ transitionDelay: `${index * 100}ms`, transitionDuration: '700ms' }}
        >
            <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:bg-primary/20 transition-colors group-hover:scale-110 transform duration-300">
                    <Icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-xl text-gray-900 group-hover:text-primary transition-colors">{feature.title}</CardTitle>
            </CardHeader>
            <CardContent>
                <CardDescription className="text-gray-600 group-hover:text-gray-700 transition-colors">
                    {feature.description}
                </CardDescription>
            </CardContent>
        </Card>
    );
}
