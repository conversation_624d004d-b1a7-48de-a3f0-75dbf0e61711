import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import {
    Target,
    MessageCircle,
    Handshake,
    ArrowRight
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import React from 'react';

interface WhyBsgItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const whyBsgItems: WhyBsgItem[] = [
    {
        icon: Target,
        title: 'Focused on Results',
        description: 'We prioritize delivering measurable outcomes that directly impact your business growth and success.',
        gradient: 'from-primary to-primary/80'
    },
    {
        icon: MessageCircle,
        title: 'Clear Communication',
        description: 'We maintain transparent and consistent communication throughout our partnership to ensure alignment.',
        gradient: 'from-secondary to-secondary/80'
    },
    {
        icon: Handshake,
        title: 'Long-term Partnership',
        description: 'We build lasting relationships based on trust, reliability, and a deep understanding of your business needs.',
        gradient: 'from-primary to-primary/80'
    }
];

export function WhyBsgSection() {
    const [isVisible, setIsVisible] = useState(false);
    const [activeTab, setActiveTab] = useState(0);
    const [isPaused, setIsPaused] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.why-bsg-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);
    
    // Add automatic slide transition every 3 seconds
    useEffect(() => {
        // Only run the interval if not paused
        if (isPaused) return;
        
        const slideInterval = setInterval(() => {
            setActiveTab((prevTab) => (prevTab + 1) % whyBsgItems.length);
        }, 3000);
        
        // Clean up interval on component unmount
        return () => clearInterval(slideInterval);
    }, [isPaused]);
    
    // Function to handle tab click with pause/resume functionality
    const handleTabClick = (index: number) => {
        setActiveTab(index);
        setIsPaused(true);
        
        // Resume automatic sliding after 5 seconds of inactivity
        const resumeTimeout = setTimeout(() => setIsPaused(false), 5000);
        return () => clearTimeout(resumeTimeout);
    };
    
    return (
        <section className="py-16 why-bsg-section relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-20">
                <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-secondary/10 rounded-full blur-3xl"></div>
            </div>
            
            <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 relative z-10">
                <div className="mb-16 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">Why Choose BSG?</h2>
                    <p className="text-xl text-gray-600">We're committed to your success through our core values and approach</p>
                </div>

                {/* Tabs Navigation */}
                <div className="flex justify-center mb-8 overflow-x-auto pb-2">
                    <div className="inline-flex rounded-lg bg-gray-100 p-1">
                        {whyBsgItems.map((item, index) => (
                            <button
                                key={item.title}
                                onClick={() => handleTabClick(index)}
                                className={cn(
                                    "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 whitespace-nowrap",
                                    activeTab === index
                                        ? "bg-white shadow-sm text-gray-900"
                                        : "text-gray-600 hover:text-gray-900"
                                )}
                            >
                                {item.title}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Tab Content */}
                <div className="bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto">
                    {whyBsgItems.map((item, index) => (
                        <div 
                            key={item.title}
                            className={cn(
                                "transition-all duration-300 transform",
                                activeTab === index 
                                    ? "opacity-100 translate-x-0" 
                                    : "opacity-0 absolute translate-x-8 pointer-events-none"
                            )}
                            style={{ display: activeTab === index ? 'block' : 'none' }}
                        >
                            <div className="flex items-start gap-6">
                                <div 
                                    className={`w-16 h-16 bg-gradient-to-br ${item.gradient} rounded-full flex items-center justify-center flex-shrink-0 transition-transform duration-500 relative`}
                                    style={{ boxShadow: `0 10px 15px -3px ${item.gradient.includes('primary') ? 'rgba(10, 58, 138, 0.2)' : 'rgba(177, 151, 99, 0.2)'}` }}
                                >
                                    <div className="absolute inset-0 rounded-full bg-white opacity-0 hover:opacity-10 transition-opacity duration-300"></div>
                                    {React.createElement(item.icon, { className: "h-8 w-8 text-white" })}
                                </div>
                                
                                <div className="flex-1">
                                    <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                                        {item.title}
                                    </h3>
                                    <p className="text-lg text-gray-600 leading-relaxed">
                                        {item.description}
                                    </p>
                                    
                                    <div className="mt-8 flex justify-end">
                                        <button 
                                            onClick={() => handleTabClick((index + 1) % whyBsgItems.length)}
                                            className="text-primary hover:text-primary-dark font-medium flex items-center gap-2 transition-colors duration-200"
                                        >
                                            Next
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Tab Indicators */}
                <div className="flex justify-center mt-6 gap-2">
                    {whyBsgItems.map((_, index) => (
                        <button 
                            key={index}
                            onClick={() => handleTabClick(index)}
                            className={cn(
                                "w-3 h-3 rounded-full transition-all duration-300",
                                activeTab === index 
                                    ? "bg-primary scale-125" 
                                    : "bg-gray-300 hover:bg-gray-400"
                            )}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface WhyBsgCardProps {
    item: WhyBsgItem;
    index: number;
    isVisible: boolean;
}

function WhyBsgCard({ item, index, isVisible }: WhyBsgCardProps) {
    const Icon = item.icon;
    
    return (
        <Card 
            className={`h-full bg-white border-gray-200 transition-all duration-500 hover:shadow-xl hover:border-primary/20 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'} group`}
            style={{ transitionDelay: `${index * 200}ms` }}
        >
            <CardHeader>
                <div 
                    className={`w-16 h-16 bg-gradient-to-br ${item.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 relative`}
                    style={{ boxShadow: `0 10px 15px -3px ${item.gradient.includes('primary') ? 'rgba(10, 58, 138, 0.2)' : 'rgba(177, 151, 99, 0.2)'}` }}
                >
                    <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                    <Icon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="mb-4 text-2xl text-gray-900 transition-colors group-hover:text-primary">
                    {item.title}
                </CardTitle>
                <CardDescription className="text-base leading-relaxed text-gray-600 group-hover:text-gray-700 transition-colors">
                    {item.description}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <div className="mt-4 text-primary font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-0 group-hover:translate-x-1">
                    Learn more <ArrowRight className="ml-2 h-4 w-4" />
                </div>
            </CardContent>
        </Card>
    );
}
