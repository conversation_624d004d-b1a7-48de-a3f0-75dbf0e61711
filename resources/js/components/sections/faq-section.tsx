import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface FaqItem {
    question: string;
    answer: string;
}

const faqItems: FaqItem[] = [
    {
        question: "What industries do you serve?",
        answer: "We specialize in (re)insurance and financial services, but our expertise extends to various industries including healthcare, technology, and manufacturing."
    },
    {
        question: "How do you ensure data security?",
        answer: "We implement enterprise-grade security measures including encryption, secure access controls, and regular security audits to protect your sensitive data."
    },
    {
        question: "What is your pricing model?",
        answer: "We offer flexible pricing models including dedicated teams, on-demand services, and comprehensive care plans. Contact us for a customized quote based on your specific needs."
    },
    {
        question: "How quickly can you get started?",
        answer: "Depending on your requirements, we can typically begin within 1-2 weeks after initial consultation and agreement on scope and terms."
    },
    {
        question: "Do you provide 24/7 support?",
        answer: "Yes, we offer 24/7 support for our Business Care Plans and On-Demand Service Support customers. Support levels vary by service tier."
    },
    {
        question: "Can you scale services up or down based on our needs?",
        answer: "Absolutely! Our flexible service model allows you to scale resources up or down based on your changing business requirements."
    }
];

export function FaqSection() {
    const [isVisible, setIsVisible] = useState(false);
    const [openItems, setOpenItems] = useState<Record<number, boolean>>({});
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.faq-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);
    
    const toggleItem = (index: number) => {
        setOpenItems(prev => ({
            ...prev,
            [index]: !prev[index]
        }));
    };
    
    return (
        <section className="overflow-hidden relative py-16 bg-gray-50 faq-section">
            {/* Background decorative elements */}
            <div className="overflow-hidden absolute inset-0 opacity-20 pointer-events-none">
                <div className="absolute top-1/4 right-1/3 w-64 h-64 rounded-full blur-3xl bg-primary/10"></div>
                <div className="absolute bottom-1/4 left-1/3 w-64 h-64 rounded-full blur-3xl bg-secondary/10"></div>
            </div>
            
            <div className="relative z-10 px-4 mx-auto max-w-4xl sm:px-6 lg:px-8">
                <div className="mb-16 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">Frequently Asked Questions</h2>
                    <p className="text-xl text-gray-600">Find answers to common questions about our services</p>
                </div>

                <div className="space-y-6">
                    {faqItems.map((item, index) => (
                        <Collapsible 
                            key={index} 
                            className={`bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} hover:border-primary/20 hover:shadow-md`}
                            style={{ transitionDelay: `${index * 150}ms` }}
                            open={openItems[index]}
                            onOpenChange={() => toggleItem(index)}
                        >
                            <CollapsibleTrigger className="flex justify-between items-center px-6 py-4 w-full text-left">
                                <h3 className="text-lg font-medium text-gray-900">{item.question}</h3>
                                <ChevronDown className={`h-5 w-5 text-gray-500 transition-transform duration-300 ${openItems[index] ? 'rotate-180 text-primary' : ''}`} />
                            </CollapsibleTrigger>
                            <CollapsibleContent className="px-6 pb-4">
                                <div className="pt-2 text-gray-600 border-t border-gray-100">
                                    {item.answer}
                                </div>
                            </CollapsibleContent>
                        </Collapsible>
                    ))}
                </div>
            </div>
        </section>
    );
}

interface FaqItemProps {
    faq: FaqItem;
    index: number;
    isOpen: boolean;
    onToggle: () => void;
}

function FaqItem({ faq, isOpen, onToggle }: Omit<FaqItemProps, 'index'>) {
    return (
        <div className="w-full">
            <Collapsible open={isOpen} onOpenChange={onToggle} className="w-full">
                <CollapsibleTrigger asChild>
                    <Button
                        variant="ghost"
                        className="overflow-hidden p-0 w-full h-auto text-left bg-white rounded-lg border border-gray-200 hover:bg-gray-50 hover:border-primary/20"
                    >
                        <div className="flex gap-3 justify-between items-start p-4 w-full min-w-0 sm:p-6 sm:gap-4">
                            <span className="flex-1 min-w-0 text-base font-semibold leading-relaxed text-left text-gray-900 break-normal sm:text-lg hyphens-auto text-wrap wrap-break-word">
                                {faq.question}
                            </span>
                            <ChevronDown
                                className={cn(
                                    "h-5 w-5 text-primary/70 transition-transform flex-shrink-0 mt-0.5",
                                    isOpen && "rotate-180"
                                )}
                            />
                        </div>
                    </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="w-full">
                    <div className="px-4 py-4 pb-4 bg-white rounded-b-lg border-r border-b border-l border-gray-200 sm:px-6 sm:pb-6">
                        <p className="leading-relaxed text-gray-600 break-words hyphens-auto">{faq.answer}</p>
                    </div>
                </CollapsibleContent>
            </Collapsible>
        </div>
    );
}
