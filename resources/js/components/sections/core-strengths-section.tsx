import { Separator } from '@/components/ui/separator';
import {
    Shield,
    Briefcase,
    Users,
    BarChart
} from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import React from 'react';
import { cn } from '@/lib/utils';

interface CoreStrengthItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const coreStrengthItems: CoreStrengthItem[] = [
    {
        icon: Shield,
        title: 'Industry Expertise',
        description: 'Deep knowledge of insurance and financial services, with specialized insights into regulatory requirements and industry best practices.',
        gradient: 'from-primary to-primary/80'
    },
    {
        icon: Briefcase,
        title: 'Business Acumen',
        description: 'Strategic understanding of operational challenges and opportunities in the financial sector, enabling us to deliver solutions that drive real business value.',
        gradient: 'from-secondary to-secondary/80'
    },
    {
        icon: Users,
        title: 'Client Focus',
        description: 'Dedicated to understanding your unique needs and delivering tailored solutions that align with your business goals and organizational culture.',
        gradient: 'from-primary to-primary/80'
    },
    {
        icon: BarChart,
        title: 'Results Orientation',
        description: 'Committed to delivering measurable outcomes and continuous improvement, with a focus on efficiency, quality, and return on investment.',
        gradient: 'from-secondary to-secondary/80'
    }
];

export function CoreStrengthsSection() {
    const [activeIndex, setActiveIndex] = useState(-1);
    const itemRefs = useRef<(HTMLDivElement | null)[]>([]);

    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const index = itemRefs.current.findIndex(ref => ref === entry.target);
                        if (index !== -1) {
                            setActiveIndex(Math.max(activeIndex, index));
                        }
                    }
                });
            },
            { threshold: 0.3, rootMargin: '0px 0px -10% 0px' }
        );

        itemRefs.current.forEach((ref) => {
            if (ref) observer.observe(ref);
        });

        return () => observer.disconnect();
    }, [activeIndex]);

    return (
        <section className="overflow-hidden relative py-20 core-strengths-section">
            {/* Background decorative elements */}
            <div className="overflow-hidden absolute inset-0 opacity-30 pointer-events-none">
                <div className="absolute top-0 left-0 w-1/2 h-1/2 rounded-full blur-3xl bg-primary/5 animate-blob"></div>
                <div className="absolute right-0 bottom-0 w-1/2 h-1/2 rounded-full blur-3xl bg-secondary/5 animate-blob animation-delay-2000"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1/3 h-1/3 rounded-full blur-3xl bg-accent/5 animate-blob animation-delay-4000"></div>
            </div>

            <div className="relative z-10 px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
                <div className="mb-20 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-5xl bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 pb-2">Our Core Strengths</h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">What sets us apart in delivering exceptional service</p>
                </div>

                {/* Timeline layout */}
                <div className="relative">
                    {/* Vertical line */}
                    <div className="hidden absolute left-1/2 w-0.5 bg-gradient-to-b from-primary/30 via-secondary/30 to-primary/30 transform -translate-x-1/2 md:block" 
                         style={{ height: `${Math.min(100, (activeIndex + 1) * 25)}%`, transition: 'height 1s ease-out' }}></div>

                    {coreStrengthItems.map((item, index) => {
                        const isEven = index % 2 === 0;
                        const isActive = index <= activeIndex;
                        return (
                            <div
                                key={item.title}
                                ref={el => itemRefs.current[index] = el}
                                className={cn(
                                    "relative mb-24 last:mb-0 transition-all duration-1000",
                                    isActive ? "opacity-100" : "opacity-0 translate-y-10"
                                )}
                                style={{ transitionDelay: `${index * 300}ms` }}
                            >
                                <div className={`flex flex-col ${isEven ? 'md:flex-row' : 'md:flex-row-reverse'} items-center gap-8`}>
                                    {/* Timeline dot - visible only on md and up */}
                                    <div className={cn(
                                        "hidden absolute left-1/2 w-6 h-6 bg-white rounded-full border-4 transform -translate-x-1/2 md:block z-10 transition-all duration-500",
                                        isActive ? "scale-100" : "scale-0",
                                        isEven ? "border-primary" : "border-secondary"
                                    )} style={{ transitionDelay: `${index * 300 + 200}ms` }}></div>

                                    {/* Content block */}
                                    <div className={cn(
                                        "md:w-5/12",
                                        isEven ? "md:text-right md:pr-16" : "md:text-left md:pl-16",
                                        "text-center md:text-left transition-all duration-700",
                                        isActive ? "opacity-100" : "opacity-0",
                                        isEven ? "md:translate-x-0" : "md:translate-x-0"
                                    )} style={{ transitionDelay: `${index * 300 + 100}ms` }}>
                                        <h3 className={cn(
                                            "mb-4 text-2xl font-bold",
                                            isEven ? "text-primary" : "text-secondary"
                                        )}>{item.title}</h3>
                                        <p className="text-gray-600 leading-relaxed">{item.description}</p>
                                    </div>

                                    {/* Icon block */}
                                    <div className={cn(
                                        "md:w-5/12",
                                        isEven ? "md:pl-16" : "md:pr-16",
                                        "flex",
                                        isEven ? "md:justify-start" : "md:justify-end",
                                        "justify-center transition-all duration-700",
                                        isActive ? "opacity-100" : "opacity-0",
                                        isEven ? "md:translate-x-0" : "md:translate-x-0"
                                    )} style={{ transitionDelay: `${index * 300 + 100}ms` }}>
                                        <div
                                            className={cn(
                                                `w-20 h-20 bg-gradient-to-br ${item.gradient} rounded-full flex items-center justify-center shadow-lg`,
                                                "transform transition-all duration-500 hover:scale-110 group relative"
                                            )}
                                        >
                                            <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                            {React.createElement(item.icon, { className: "h-10 w-10 text-white" })}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </section>
    );
}
