import { Link, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { type SharedData } from '@/types';
import {
    ArrowRight,
    Mail,
    User,
    MessageSquare,
    Send,
    Shield,
    Globe,
    CheckCircle,
    ArrowUpRight
} from 'lucide-react';
import { useState, useEffect } from 'react';

export function HeroSection() {
    const { auth } = usePage<SharedData>().props;
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        message: ''
    });

    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        // Set visibility after component mounts for animations
        setIsVisible(true);
    }, []);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle form submission - this would typically send data to a backend endpoint
        console.log('Form submitted:', formData);
        // Reset form after submission
        setFormData({ name: '', email: '', message: '' });
        // Show success message or redirect
    };

    return (
        <section className="overflow-hidden relative pt-24 pb-16 bg-gradient-to-br from-blue-50 via-white to-blue-50">
            {/* Background animated elements */}
            <div className="overflow-hidden absolute top-0 left-0 w-full h-full pointer-events-none">
                <div className="absolute left-10 top-20 w-64 h-64 rounded-full mix-blend-multiply bg-primary/5 animate-blob"></div>
                <div className="absolute right-10 top-40 w-72 h-72 rounded-full mix-blend-multiply bg-secondary/5 animate-blob animation-delay-2000"></div>
                <div className="absolute -bottom-8 left-1/3 w-56 h-56 rounded-full mix-blend-multiply bg-primary/5 animate-blob animation-delay-4000"></div>
            </div>

            <div className="relative z-10 px-4 mx-auto max-w-7xl">
                <div className="grid grid-cols-1 gap-16 items-center lg:grid-cols-2">
                    {/* Left Content */}
                    <div className={`text-center lg:text-left transform transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
                        <Badge variant="outline" className="mb-6 animate-pulse text-primary border-primary/20 bg-white/50">
                            Trust Us as Your Strategic Partner
                        </Badge>
                        <h1 className="mb-6 text-4xl font-bold leading-tight text-gray-900 md:text-5xl lg:text-6xl">
                            Scale Smarter. <span className="text-secondary">Grow Faster.</span>
                        </h1>
                        <p className="mb-4 text-xl font-medium text-gray-700">
                            with <span className="font-bold text-transparent bg-clip-text bg-secondary">Backsure Global Support</span>
                        </p>
                        <p className="mb-6 text-lg leading-relaxed text-gray-600">
                            In today's competitive landscape, success requires a new approach. At BSG Support, we don't follow the usual path — we redefine it.
                            We challenge business-as-usual through insight, innovation, and real, measurable outcomes.
                        </p>

                        <div className="flex flex-wrap gap-4 mb-8">
                            <div className="flex items-center space-x-2">
                                <CheckCircle className="w-5 h-5 text-primary" />
                                <span className="text-gray-700">Reduce Risk</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <CheckCircle className="w-5 h-5 text-primary" />
                                <span className="text-gray-700">Control Costs</span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <CheckCircle className="w-5 h-5 text-primary" />
                                <span className="text-gray-700">Drive Growth</span>
                            </div>
                        </div>

                        <div className="flex flex-col gap-4 justify-center items-center sm:flex-row lg:justify-start">
                            <Button size="lg" className="px-8 py-3 text-white shadow-lg transition-all bg-primary hover:bg-primary/90 hover:shadow-xl group" asChild>
                                <Link href="/contact">
                                    Talk to Our Team
                                    <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
                                </Link>
                            </Button>
                            <Button variant="outline" className="border-primary/20 text-primary hover:bg-primary/5 group" asChild>
                                <Link href="/services">
                                    Our Services
                                    <ArrowUpRight className="ml-2 h-4 w-4 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-transform" />
                                </Link>
                            </Button>
                        </div>
                    </div>

                    {/* Right Contact Form */}
                    <div className={`relative lg:order-last transform transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
                        <div className="relative">
                            {/* Background decorative elements */}
                            <div className="absolute -top-4 -right-4 w-72 h-72 rounded-full opacity-20 blur-3xl animate-pulse bg-primary/10"></div>
                            <div className="absolute -bottom-8 -left-8 w-64 h-64 rounded-full opacity-20 blur-3xl animate-pulse bg-secondary/10 animation-delay-2000"></div>

                            {/* Contact Form Container */}
                            <div className="relative p-8 bg-white rounded-2xl border border-gray-100 shadow-2xl transition-shadow duration-300 hover:shadow-primary/10">
                                <div className="flex justify-between items-center pb-4 mb-6 border-b border-gray-200">
                                    <div className="flex items-center space-x-3">
                                        <div className="flex justify-center items-center w-8 h-8 rounded-lg bg-primary">
                                            <MessageSquare className="w-5 h-5 text-white" />
                                        </div>
                                        <span className="font-semibold text-gray-900">Contact Us</span>
                                    </div>
                                </div>

                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <label htmlFor="name" className="block mb-1 text-sm font-medium text-gray-700">Name</label>
                                        <div className="relative">
                                            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                                                <User className="w-5 h-5 text-gray-400" />
                                            </div>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleChange}
                                                className="px-4 py-2 pl-10 w-full rounded-md border border-gray-300 transition outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                                                placeholder="Your name"
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <label htmlFor="email" className="block mb-1 text-sm font-medium text-gray-700">Email</label>
                                        <div className="relative">
                                            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                                                <Mail className="w-5 h-5 text-gray-400" />
                                            </div>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                value={formData.email}
                                                onChange={handleChange}
                                                className="px-4 py-2 pl-10 w-full rounded-md border border-gray-300 transition outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                                                placeholder="Your email"
                                                required
                                            />
                                        </div>
                                    </div>

                                    <div>
                                        <label htmlFor="message" className="block mb-1 text-sm font-medium text-gray-700">Message</label>
                                        <div className="relative">
                                            <div className="flex absolute top-3 left-3 items-start pointer-events-none">
                                                <MessageSquare className="w-5 h-5 text-gray-400" />
                                            </div>
                                            <textarea
                                                id="message"
                                                name="message"
                                                value={formData.message}
                                                onChange={handleChange}
                                                rows={4}
                                                className="px-4 py-2 pl-10 w-full rounded-md border border-gray-300 transition outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                                                placeholder="How can we help you?"
                                                required
                                            ></textarea>
                                        </div>
                                    </div>

                                    <button
                                        type="submit"
                                        className="flex justify-center items-center px-6 py-3 w-full font-medium text-white rounded-md transition-colors duration-200 bg-primary hover:bg-primary/90 group"
                                    >
                                        <Send className="mr-2 w-4 h-4 transition-transform group-hover:translate-x-1" />
                                        Send Message
                                    </button>
                                </form>
                            </div>

                            {/* Floating elements */}
                            <div className="absolute -top-6 -left-6 p-3 bg-white rounded-lg border border-gray-200 shadow-lg animate-float">
                                <div className="flex items-center space-x-2">
                                    <Shield className="w-4 h-4 text-primary" />
                                    <span className="text-xs font-medium text-gray-700">Secure</span>
                                </div>
                            </div>

                            <div className="absolute -bottom-4 -right-6 p-3 bg-white rounded-lg border border-gray-200 shadow-lg animate-float animation-delay-1000">
                                <div className="flex items-center space-x-2">
                                    <Globe className="w-4 h-4 text-secondary" />
                                    <span className="text-xs font-medium text-gray-700">Global</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
