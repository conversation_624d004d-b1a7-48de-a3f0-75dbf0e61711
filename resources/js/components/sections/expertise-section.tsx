import {
    Bar<PERSON>hart<PERSON>,
    <PERSON><PERSON><PERSON>,
    Zap,
    TrendingUp
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { Separator } from '@/components/ui/separator';
import React from 'react';

interface ExpertiseItem {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    gradient: string;
}

const expertiseItems: ExpertiseItem[] = [
    {
        icon: BarChart3,
        title: 'Assessment of Requirements',
        description: 'Our systematic approach to understanding your business needs ensures we deliver precisely what you need through detailed analysis and collaborative sessions.',
        gradient: 'from-primary to-primary'
    },
    {
        icon: Settings,
        title: 'Process Mapping',
        description: 'We document and visualize your operational workflows, identifying optimization opportunities and streamlining critical processes.',
        gradient: 'from-secondary to-secondary'
    },
    {
        icon: Zap,
        title: 'Workflow Automation',
        description: 'Streamline operational tasks through smart automation—reducing effort, improving accuracy, and enhancing your team\'s impact.',
        gradient: 'from-primary to-primary'
    },
    {
        icon: TrendingUp,
        title: 'Performance Monitoring',
        description: 'We track key metrics and provide regular insights, helping you make data-driven decisions and continuously improve operations.',
        gradient: 'from-secondary to-secondary'
    }
];

export function ExpertiseSection() {
    const [isVisible, setIsVisible] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.expertise-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);
    
    return (
        <section className="py-16 bg-gray-50 expertise-section relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
                <div className="absolute top-0 left-0 w-1/2 h-1/2 bg-primary/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-secondary/5 rounded-full blur-3xl"></div>
            </div>
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Leverage Our Expertise</h2>
                    <p className="text-xl text-gray-600">Transform your operations with our proven methodologies</p>
                </div>

                {/* Horizontal layout with separators */}
                <div className="bg-white rounded-xl shadow-lg p-8">
                    {expertiseItems.map((item, index) => (
                        <div key={item.title}>
                            <ExpertiseRow 
                                item={item} 
                                index={index} 
                                isVisible={isVisible}
                            />
                            {index < expertiseItems.length - 1 && (
                                <Separator className="my-8 opacity-30" />
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}

interface ExpertiseRowProps {
    item: ExpertiseItem;
    index: number;
    isVisible: boolean;
}

function ExpertiseRow({ item, index, isVisible }: ExpertiseRowProps) {
    const Icon = item.icon;
    
    return (
        <div 
            className={`flex flex-col md:flex-row items-start md:items-center gap-6 transform transition-all duration-700 ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-10 opacity-0'}`}
            style={{ transitionDelay: `${index * 150}ms` }}
        >
            <div 
                className={`w-16 h-16 bg-gradient-to-br ${item.gradient} rounded-full flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-500 relative`}
                style={{ boxShadow: `0 10px 15px -3px ${item.gradient.includes('primary') ? 'rgba(10, 58, 138, 0.2)' : 'rgba(177, 151, 99, 0.2)'}` }}
            >
                <div className="absolute inset-0 rounded-full bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                <Icon className="h-8 w-8 text-white" />
            </div>
            
            <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary transition-colors duration-300">
                    {item.title}
                </h3>
                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                    {item.description}
                </p>
            </div>
            
            <div className="hidden md:flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full text-gray-400 text-xs font-bold">
                {index + 1}
            </div>
        </div>
    );
}
