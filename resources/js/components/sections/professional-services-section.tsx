import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Users,
    Zap,
    Shield,
    CheckCircle,
    ArrowRight
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface Service {
    icon: React.ComponentType<{ className?: string }>;
    title: string;
    description: string;
    features: string[];
    gradient: string;
    href: string; // Added href to the interface
}

const services: Service[] = [
    {
        icon: Users,
        title: 'Dedicated Team',
        description: 'Boost your business performance with our skilled, full-time teams. Get your own dedicated team of professionals working exclusively on your projects with full commitment and focus.',
        features: [
            'Exclusive resources for your projects',
            'Customized team structure',
            'Long-term collaboration'
        ],
        gradient: 'from-primary to-primary',
        href: '/pg_layouts/dedicatedteam' // Added href
    },
    {
        icon: Zap,
        title: 'On-Demand Service Support',
        description: 'Get expert help when you need it without the overhead. Flexible support services available whenever you need them, scaling up or down based on your requirements.',
        features: [
            'Pay-as-you-go model',
            '24/7 availability',
            'Quick response times'
        ],
        gradient: 'from-secondary to-secondary',
        href: '/pg_layouts/ondemand' // Added href
    },
    {
        icon: Shield,
        title: 'Business Care Plans',
        description: 'We manage the back office, so you can focus on growth. Comprehensive care packages designed to maintain and optimize your business operations and infrastructure.',
        features: [
            'Regular maintenance',
            'Proactive monitoring',
            'Priority support'
        ],
        gradient: 'from-primary to-primary',
        href: '/pg_layouts/business' // Added href
    }
];

export function ProfessionalServicesSection() {
    const [isVisible, setIsVisible] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.disconnect();
                }
            },
            { threshold: 0.1 }
        );
        
        const section = document.querySelector('.services-section');
        if (section) observer.observe(section);
        
        return () => observer.disconnect();
    }, []);
    
    return (
        <section className="py-16 bg-gray-50 services-section relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
                <div className="absolute top-20 right-20 w-96 h-96 bg-primary/5 rounded-full blur-3xl"></div>
                <div className="absolute -bottom-20 -left-20 w-96 h-96 bg-secondary/5 rounded-full blur-3xl"></div>
            </div>
            
            <div className="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8 relative z-10">
                <div className="mb-16 text-center">
                    <h2 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">Our Professional Services</h2>
                    <p className="text-xl text-gray-600">Tailored solutions to meet your business needs and drive growth</p>
                </div>

                <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
                    {services.map((service, index) => (
                        <ServiceCard 
                            key={service.title} 
                            service={service} 
                            index={index} 
                            isVisible={isVisible}
                        />
                    ))}
                </div>
            </div>
        </section>
    );
}

interface ServiceCardProps {
    service: Service;
    index: number;
    isVisible: boolean;
}

function ServiceCard({ service, index, isVisible }: ServiceCardProps) {
    const Icon = service.icon;
    
    return (
        <a 
            href={service.href} 
            className="block hover:no-underline focus:no-underline group"
        >
            <Card 
                className={`h-full bg-white border-gray-200 transition-all duration-500 group-hover:shadow-xl group-hover:border-primary/20 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}
                style={{ transitionDelay: `${index * 200}ms` }}
            >
                <CardHeader>
                    <div 
                        className={`w-16 h-16 bg-gradient-to-br ${service.gradient} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-500 relative`}
                        style={{ boxShadow: `0 10px 15px -3px ${service.gradient.includes('primary') ? 'rgba(10, 58, 138, 0.2)' : 'rgba(177, 151, 99, 0.2)'}` }}
                    >
                        <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                        <Icon className="w-8 h-8 text-white" />
                    </div>
                    <CardTitle className="mb-4 text-2xl text-gray-900 transition-colors group-hover:text-primary">
                        {service.title}
                    </CardTitle>
                    <CardDescription className="text-base leading-relaxed text-gray-600">
                        {service.description}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <ul className="space-y-3">
                        {service.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center text-gray-600 group-hover:text-gray-700 transition-colors">
                                <CheckCircle className="flex-shrink-0 mr-3 w-5 h-5 text-secondary" />
                                {feature}
                            </li>
                        ))}
                    </ul>
                    
                    <div className="mt-6 text-primary font-medium flex items-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-0 group-hover:translate-x-1">
                        Learn more <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                </CardContent>
            </Card>
        </a>
    );
}