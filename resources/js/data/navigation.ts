import { type NavItem } from '@/types';
import {
    BookOpen,
    Briefcase,
    Building,
    Calendar,
    ClipboardList,
    Clock,
    CreditCard,
    DollarSign,
    FileBarChart,
    FileCheck,
    FileText,
    FileX,
    HelpCircle,
    Home,
    Info,
    Lock,
    LogIn,
    MessageSquare,
    Phone,
    Scale,
    Search,
    Settings,
    Shield,
    ThumbsUp,
    Umbrella,
    UserCheck,
    UserPlus,
    Users,
} from 'lucide-react';

// Main navigation items for the public website
export const mainNavigation: NavItem[] = [

    {
        title: 'Home',
        href: route('home'),
        icon: Home,
        description: 'BGS Home',
    },
    {
        title: 'Solutions',
        href: '/solutions',
        icon: Settings,
        children: [
            {
                title: 'Insurance',
                href: route('insurance'),
                icon: Umbrella,
                description: 'Comprehensive insurance solutions',
            },
            {
                title: 'Finance & Accounting',
                href: route('finance'),
                icon: CreditCard,
                description: 'Financial management services',
            },
            {
                title: 'HR Management',
                href: route('hr-management'),
                icon: Users,
                description: 'Human resources solutions',
            },
            {
                title: 'Compliance and Admin',
                href: route('compliance'),
                icon: FileCheck,
                description: 'Regulatory compliance support',
            },
        ],
    },
    {
        title: 'Services',
        href: '/services',
        icon: Briefcase,
        children: [
            {
                title: 'Dedicated Teams',
                href: route('dedicated-teams'),
                icon: UserCheck,
                description: 'Personalized assistance for your workforce',
            },
            {
                title: 'OnDemand Services',
                href: route('on-demand'),
                icon: Clock,
                description: 'Immediate help whenever you need it',
            },
            {
                title: 'Business Care Plans',
                href: route('business-care'),
                icon: Shield,
                description: 'Customized support plans for long-term success',
            },            
            
        ],
    },      
    

    {
        title: 'Case Studies',
        href: route('case-studies'),
        icon: FileBarChart,
        description: 'Success stories and case studies',
    },
    
  
    
    {
        title: 'Blog',
        href: route('blog'),
        icon: MessageSquare,
        description: 'Latest business insights and resources',
    },   
    
        
    {
        title: 'Data Security',
        href: route('security'),
        icon: Lock,
        description: 'Security measures and compliance',
    },
    {
        title: 'Pricing',
        href: route('pricing'),
        icon: DollarSign,
        description: 'Transparent pricing plans',
    },
    {
        title: 'About Us',
        href: '/about',
        icon: Building,
        children: [
            {
                title: 'About Us',
                href: route('about'),
                icon: Info,
                description: 'Know who we are and what we stand for',
            },
            {
                title: 'Team',
                href: route('team'),
                icon: Users,
                description: 'Meet our team',
            },
            {
                title: 'Careers',
                href: route('careers'),
                icon: Briefcase,
                description: 'Join our team',
            },
            {
                title: 'Testimonials',
                href: route('testimonials'),
                icon: ThumbsUp,
                description: 'Client testimonials',
            },
            {
                title: 'FAQ',
                href: route('faq'),
                icon: HelpCircle,
                description: 'Frequently asked questions',
            },

        ],
    },
    {
        title: 'Contact Us',
        href: route('contact'),
        icon: Phone,
        description: 'Get in touch with us',
    },
];

// Right side navigation items
export const rightNavigation: NavItem[] = [
    // {
    //     title: 'Contact Us',
    //     href: '/contact',
    //     icon: Phone,
    //     description: 'Get in touch with us'
    // }
];

// Authentication navigation items
export const authNavigation: NavItem[] = [
    {
        title: 'Login',
        href: route('login'),
        icon: LogIn,
        description: 'Sign in to your account',
    },
    {
        title: 'Forgot Password',
        href: route('password.request'),
        icon: FileX,
        description: 'Reset your password',
    },
    {
        title: 'Sign Up',
        href: route('register'),
        icon: UserPlus,
        description: 'Create a new account',
    },
    {
        title: 'Terms & Conditions',
        href: route('terms'),
        icon: Scale,
        description: 'Terms and conditions',
    },
];

// Mobile navigation - flattened structure for easier mobile navigation
export const mobileNavigation: NavItem[] = [...mainNavigation, ...rightNavigation, ...authNavigation];
