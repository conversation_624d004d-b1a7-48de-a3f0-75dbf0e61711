<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

Route::get('/case-studies', function() {
    return Inertia::render('pg_layouts/casestudy');
})->name('case-studies');

Route::get('/insurance', function() {
    return Inertia::render('pg_layouts/insurance');
})->name('insurance');

Route::get('/finance', function() {
    return Inertia::render('pg_layouts/finance');
})->name('finance');


Route::get('/dedicated-teams', function() {
    return Inertia::render('pg_layouts/dedicated');
})->name('dedicated-teams');

Route::get('/on-demand', function() {
    return Inertia::render('pg_layouts/ondemand');
})->name('on-demand');

Route::get('/business-care', function() {
    return Inertia::render('pg_layouts/business');
})->name('business-care');

Route::get('/about', function() {
    return Inertia::render('pg_layouts/about');
})->name('about');

Route::get('/team', function() {
    return Inertia::render('pg_layouts/team');
})->name('team');

Route::get('/careers', function() {
    return Inertia::render('pg_layouts/career');
})->name('careers');

Route::get('/faq', function() {
    return Inertia::render('pg_layouts/faq');
})->name('faq');

Route::get('/testimonials', function() {
    return Inertia::render('pg_layouts/testimonial');
})->name('testimonials');

Route::get('/hr-management', function() {
    return Inertia::render('pg_layouts/hr');
})->name('hr-management');


Route::get('/compliance', function() {
    return Inertia::render('pg_layouts/compliance');
})->name('compliance');

Route::get('/blog', function() {
    return Inertia::render('pg_layouts/blog');
})->name('blog');

Route::get('/contact', function() {
    return Inertia::render('pg_layouts/contact');
})->name('contact');


Route::get('/pricing', function() {
    return Inertia::render('pg_layouts/price');
})->name('pricing');

Route::get('/security', function() {
    return Inertia::render('pg_layouts/security');
})->name('security');

Route::get('/documentation', function() {
    return Inertia::render('pg_layouts/document');
})->name('documentation');

Route::get('/privacy', function() {
    return Inertia::render('pg_layouts/privacy');
})->name('privacy');

Route::get('/terms', function() {
    return Inertia::render('pg_layouts/terms');
})->name('terms');

Route::get('/cookies', function() {
    return Inertia::render('pg_layouts/cookies');
})->name('cookies');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
