<svg width="1000" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
    </pattern>
    <pattern id="circles" width="100" height="100" patternUnits="userSpaceOnUse">
      <circle cx="50" cy="50" r="15" fill="none" stroke="white" stroke-width="1" opacity="0.2"/>
    </pattern>
    <pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="white" opacity="0.2"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>
  <rect width="100%" height="100%" fill="url(#circles)"/>
  <rect width="100%" height="100%" fill="url(#dots)"/>
</svg>