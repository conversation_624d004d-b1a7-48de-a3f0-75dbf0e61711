<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  
  <!-- Gradient overlay -->
  <rect width="100%" height="100%" fill="url(#grad1)"/>
  
  <!-- Define gradient -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#b19763;stop-opacity:0.2"/>
      <stop offset="100%" style="stop-color:#062767;stop-opacity:0.3"/>
    </linearGradient>
  </defs>
  
  <!-- Partnership illustration -->
  <!-- First entity -->
  <circle cx="300" cy="300" r="80" fill="#ffffff" stroke="#062767" stroke-width="3"/>
  <text x="300" y="280" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#062767" font-weight="bold">YOUR</text>
  <text x="300" y="310" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#062767" font-weight="bold">BUSINESS</text>
  
  <!-- Second entity -->
  <circle cx="500" cy="300" r="80" fill="#ffffff" stroke="#b19763" stroke-width="3"/>
  <text x="500" y="280" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#b19763" font-weight="bold">OUTSOURCING</text>
  <text x="500" y="310" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="#b19763" font-weight="bold">PARTNER</text>
  
  <!-- Handshake -->
  <line x1="380" y1="300" x2="420" y2="300" stroke="#062767" stroke-width="6"/>
  
  <!-- Left hand -->
  <path d="M380,300 C375,290 365,295 360,300 C355,305 355,315 360,320 C365,325 375,320 380,310 Z" 
        fill="#062767" stroke="#ffffff" stroke-width="1"/>
  
  <!-- Right hand -->
  <path d="M420,300 C425,290 435,295 440,300 C445,305 445,315 440,320 C435,325 425,320 420,310 Z" 
        fill="#b19763" stroke="#ffffff" stroke-width="1"/>
  
  <!-- Benefits -->
  <!-- Benefit 1 -->
  <circle cx="250" cy="180" r="30" fill="#ffffff" stroke="#062767" stroke-width="2"/>
  <text x="250" y="185" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#062767">$</text>
  <line x1="270" y1="200" x2="300" y2="220" stroke="#062767" stroke-width="2" stroke-dasharray="5,3"/>
  
  <!-- Benefit 2 -->
  <circle cx="400" cy="150" r="30" fill="#ffffff" stroke="#b19763" stroke-width="2"/>
  <text x="400" y="155" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="#b19763">⚙️</text>
  <line x1="400" y1="180" x2="400" y2="220" stroke="#b19763" stroke-width="2" stroke-dasharray="5,3"/>
  
  <!-- Benefit 3 -->
  <circle cx="550" cy="180" r="30" fill="#ffffff" stroke="#062767" stroke-width="2"/>
  <text x="550" y="185" font-family="Arial, sans-serif" font-size="20" text-anchor="middle" fill="#062767">⏱️</text>
  <line x1="530" y1="200" x2="500" y2="220" stroke="#062767" stroke-width="2" stroke-dasharray="5,3"/>
  
  <!-- Checklist -->
  <rect x="350" y="380" width="100" height="120" fill="#ffffff" stroke="#b19763" stroke-width="2"/>
  <line x1="370" y1="410" x2="430" y2="410" stroke="#062767" stroke-width="1"/>
  <line x1="370" y1="430" x2="430" y2="430" stroke="#062767" stroke-width="1"/>
  <line x1="370" y1="450" x2="430" y2="450" stroke="#062767" stroke-width="1"/>
  <line x1="370" y1="470" x2="430" y2="470" stroke="#062767" stroke-width="1"/>
  
  <polyline points="360,410 365,415 370,405" stroke="#b19763" stroke-width="2" fill="none"/>
  <polyline points="360,430 365,435 370,425" stroke="#b19763" stroke-width="2" fill="none"/>
  <polyline points="360,450 365,455 370,445" stroke="#b19763" stroke-width="2" fill="none"/>
  
  <!-- Text -->
  <text x="400" y="520" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#062767" font-weight="bold">Choosing the Right Partner</text>
</svg>